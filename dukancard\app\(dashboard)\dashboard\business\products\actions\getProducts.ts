"use server";

import { createClient } from "@/utils/supabase/server";

import { ProductFilters, ProductSortBy } from "./types";
import { ProductWithVariantInfo } from "@/types/products";

// Fetch all products/services with variant information
export async function getProductServices(
  page: number = 1,
  limit: number = 10,
  filters: ProductFilters = {},
  sortBy: ProductSortBy = "created_desc"
): Promise<{
  data?: ProductWithVariantInfo[];
  count?: number;
  error?: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user) return { error: "User not authenticated." };

  const offset = (page - 1) * limit;
  let query = supabase
    .from("products_services")
    .select(
      `
      id,
      business_id,
      product_type,
      name,
      description,
      base_price,
      discounted_price,
      is_available,
      image_url,
      images,
      featured_image_index,
      created_at,
      updated_at,
      slug,
      product_variants(id, is_available)
    `,
      { count: "exact" }
    )
    .eq("business_id", user.id);

  // Apply Filters
  if (filters.searchTerm)
    query = query.or(
      `name.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`
    );
  if (filters.hasVariants !== undefined) {
    if (filters.hasVariants) {
      // Only products that have variants
      query = query.not("product_variants", "is", null);
    } else {
      // Only products that don't have variants
      query = query.is("product_variants", null);
    }
  }
  if (filters.productType)
    query = query.eq("product_type", filters.productType);

  // Apply Sorting
  switch (sortBy) {
    case "created_asc":
      query = query.order("created_at", { ascending: true });
      break;
    case "price_asc":
      // Sort by discounted_price first, then base_price for price_asc
      query = query
        .order("discounted_price", { ascending: true, nullsFirst: false })
        .order("base_price", { ascending: true, nullsFirst: false });
      break;
    case "price_desc":
      // Sort by discounted_price first, then base_price for price_desc
      query = query
        .order("discounted_price", { ascending: false, nullsFirst: false })
        .order("base_price", { ascending: false, nullsFirst: false });
      break;
    case "name_asc":
      query = query.order("name", { ascending: true });
      break;
    case "name_desc":
      query = query.order("name", { ascending: false });
      break;
    case "available_first":
      // Sort by availability first (available = true first), then by created_at desc
      query = query
        .order("is_available", { ascending: false })
        .order("created_at", { ascending: false });
      break;
    case "unavailable_first":
      // Sort by availability first (unavailable = false first), then by created_at desc
      query = query
        .order("is_available", { ascending: true })
        .order("created_at", { ascending: false });
      break;
    case "created_desc":
    default:
      query = query.order("created_at", { ascending: false });
      break;
  }

  query = query.range(offset, offset + limit - 1);
  const { data, error, count } = await query;

  if (error) {
    console.error("Fetch Products Error:", error);
    return { error: "Failed to fetch products/services." };
  }

  // Transform data to include variant information
  const transformedData: ProductWithVariantInfo[] = (data || []).map(
    (product: { product_variants: { is_available: boolean }[] | null; [key: string]: unknown }) => {
      const variants =
        (product as { product_variants: { is_available: boolean }[] | null })
          .product_variants || [];
      const variant_count = variants.length;
      const available_variant_count = variants.filter(
        (v: { is_available: boolean }) => v.is_available
      ).length;
      const has_variants = variant_count > 0;

      return {
        id: product.id as string,
        business_id: product.business_id as string,
        product_type: product.product_type as "physical" | "service",
        name: product.name as string,
        description: product.description as string | null,
        base_price: product.base_price as number,
        discounted_price: product.discounted_price as number | null,
        is_available: product.is_available as boolean,
        image_url: product.image_url as string | null,
        images: product.images as string[] || [],
        featured_image_index: product.featured_image_index as number,
        slug: product.slug as string | null,
        created_at: new Date(product.created_at as string).toISOString(),
        updated_at: new Date(product.updated_at as string).toISOString(),
        variant_count,
        has_variants,
        available_variant_count,
      };
    }
  );

  // Apply variant count sorting if needed (post-processing)
  if (sortBy === "variant_count_asc") {
    transformedData.sort((a, b) => a.variant_count - b.variant_count);
  } else if (sortBy === "variant_count_desc") {
    transformedData.sort((a, b) => b.variant_count - a.variant_count);
  }

  return { data: transformedData, count: count ?? 0 };
}
