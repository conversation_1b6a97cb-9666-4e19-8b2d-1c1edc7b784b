/**
 * Centralized Discovery Service
 * Handles all search, filter, and sort operations for the discovery screen
 * Eliminates redundancies and provides consistent behavior
 */

import { supabase } from "../../config/supabase";
import {
  BusinessSortBy,
  BusinessCardData,
  NearbyProduct,
  ProductSortOption,
} from "../../types/discovery";
import { applySorting } from "./utils/secureBusinessProfiles";

// Types
export interface DiscoverySearchParams {
  viewType: "cards" | "products";
  searchTerm?: string | null;
  category?: string | null;
  pincode?: string | null;
  city?: string | null;
  locality?: string | null;
  page?: number;
  limit?: number;
  businessSort?: BusinessSortBy;
  productSort?: ProductSortOption;
  productType?: "physical" | "service" | null;
  userLocation?: { latitude: number; longitude: number };
}

export interface DiscoveryResult {
  businesses?: BusinessCardData[];
  products?: NearbyProduct[];
  totalCount: number;
  hasMore: boolean;
  nextPage: number | null;
  isAuthenticated: boolean;
  location?: { city: string; state: string } | null;
}

export interface NormalizedParams {
  viewType: "cards" | "products";
  searchTerm: string | null;
  category: string | null;
  pincode: string | null;
  city: string | null;
  locality: string | null;
  page: number;
  limit: number;
  businessSort: BusinessSortBy;
  productSort: ProductSortOption;
  productType: "physical" | "service" | null;
}

/**
 * Centralized Discovery Service Class
 */
export class DiscoveryService {
  private supabase = supabase;

  /**
   * Main search method - handles all discovery operations
   */
  async search(params: DiscoverySearchParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      // Validate and normalize parameters
      const normalizedParams = this.validateAndNormalizeParams(params);

      this.logOperation("search", normalizedParams);

      // Route to appropriate search method based on view type
      if (normalizedParams.viewType === "products") {
        return await this.searchProducts(normalizedParams);
      } else {
        return await this.searchBusinesses(normalizedParams);
      }
    } catch (error) {
      console.error("DiscoveryService.search error:", error);
      return { error: "An unexpected error occurred during search" };
    }
  }

  /**
   * Search for products
   */
  private async searchProducts(params: NormalizedParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      const offset = (params.page - 1) * params.limit;

      // Step 1: Get valid business IDs based on filters
      const validBusinessIds = await this.getValidBusinessIds(params);

      if (validBusinessIds.length === 0) {
        return {
          data: {
            products: [],
            totalCount: 0,
            hasMore: false,
            nextPage: null,
            isAuthenticated: true,
          },
        };
      }

      // Step 2: Count total products
      const totalCount = await this.countProducts(validBusinessIds, params);

      // Step 3: Fetch products
      const products = await this.fetchProducts(
        validBusinessIds,
        params,
        offset
      );

      // Step 4: Calculate pagination
      const hasMore = totalCount > offset + products.length;
      const nextPage = hasMore ? params.page + 1 : null;

      return {
        data: {
          products,
          totalCount,
          hasMore,
          nextPage,
          isAuthenticated: true,
        },
      };
    } catch (error) {
      console.error("DiscoveryService.searchProducts error:", error);
      return { error: "Failed to search products" };
    }
  }

  /**
   * Search for businesses
   */
  private async searchBusinesses(params: NormalizedParams): Promise<{
    data?: DiscoveryResult;
    error?: string;
  }> {
    try {
      const offset = (params.page - 1) * params.limit;

      // Build business query with all filters
      let countQuery = this.supabase
        .from("business_profiles")
        .select("id", { count: "exact" })
        .eq("status", "online");

      let businessQuery = this.supabase
        .from("business_profiles")
        .select(
          `
          id, business_name, logo_url, member_name, title,
          address_line, city, state, pincode, locality, phone, instagram_url,
          facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
          delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
          business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
          custom_branding, custom_ads, latitude, longitude
        `
        )
        .eq("status", "online");

      // Apply all filters
      [countQuery, businessQuery] = this.applyBusinessFilters(
        countQuery,
        businessQuery,
        params
      );

      // Apply sorting and pagination to business query
      businessQuery = applySorting(businessQuery, params.businessSort);
      businessQuery = businessQuery.range(offset, offset + params.limit - 1);

      // Execute queries
      const [countResult, dataResult] = await Promise.all([
        countQuery,
        businessQuery,
      ]);

      if (countResult.error) {
        console.error("Business count query error:", countResult.error);
        return { error: "Failed to count businesses" };
      }

      if (dataResult.error) {
        console.error("Business data query error:", dataResult.error);
        return { error: "Failed to fetch businesses" };
      }

      // Process results
      const totalCount = countResult.count || 0;
      const businesses = this.processBusinessData(dataResult.data || []);
      const hasMore = totalCount > offset + businesses.length;
      const nextPage = hasMore ? params.page + 1 : null;

      return {
        data: {
          businesses,
          totalCount,
          hasMore,
          nextPage,
          isAuthenticated: true,
        },
      };
    } catch (error) {
      console.error("DiscoveryService.searchBusinesses error:", error);
      return { error: "Failed to search businesses" };
    }
  }

  /**
   * Get valid business IDs based on location and category filters
   */
  private async getValidBusinessIds(
    params: NormalizedParams
  ): Promise<string[]> {
    console.log("🔍 getValidBusinessIds called with filters:", {
      city: params.city,
      pincode: params.pincode,
      locality: params.locality,
      category: params.category,
    });

    let businessQuery = this.supabase
      .from("business_profiles")
      .select("id")
      .eq("status", "online");

    // Apply location filters
    if (params.city) {
      businessQuery = businessQuery.eq("city", params.city);
      console.log("📍 Applied city filter:", params.city);
    }
    if (params.pincode) {
      businessQuery = businessQuery.eq("pincode", params.pincode);
      console.log("📍 Applied pincode filter:", params.pincode);
    }
    if (params.locality) {
      businessQuery = businessQuery.eq("locality", params.locality);
      console.log("📍 Applied locality filter:", params.locality);
    }

    // Apply category filter - this is crucial for category filtering to work
    if (params.category) {
      businessQuery = businessQuery.eq("business_category", params.category);
      console.log("🏷️ Applied category filter:", params.category);
    }

    const { data, error } = await businessQuery;

    if (error) {
      console.error("❌ Error fetching valid business IDs:", error);
      return [];
    }

    const businessIds = data?.map((b: any) => b.id) || [];
    console.log("✅ Found valid business IDs:", {
      count: businessIds.length,
      ids: businessIds.slice(0, 5), // Log first 5 IDs for debugging
    });

    return businessIds;
  }

  /**
   * Count total products from valid businesses
   */
  private async countProducts(
    validBusinessIds: string[],
    params: NormalizedParams
  ): Promise<number> {
    let countQuery = this.supabase
      .from("products_services")
      .select("id", { count: "exact" })
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Apply product-specific filters
    if (params.productType) {
      countQuery = countQuery.eq("product_type", params.productType);
    }
    if (params.searchTerm) {
      countQuery = countQuery.ilike("name", `%${params.searchTerm}%`);
    }

    const { count, error } = await countQuery;

    if (error) {
      console.error("Error counting products:", error);
      return 0;
    }

    return count || 0;
  }

  /**
   * Fetch products from valid businesses
   */
  private async fetchProducts(
    validBusinessIds: string[],
    params: NormalizedParams,
    offset: number
  ): Promise<NearbyProduct[]> {
    let productsQuery = this.supabase
      .from("products_services")
      .select(
        `
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug, latitude, longitude)
      `
      )
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Apply product-specific filters
    if (params.productType) {
      productsQuery = productsQuery.eq("product_type", params.productType);
    }
    if (params.searchTerm) {
      productsQuery = productsQuery.ilike("name", `%${params.searchTerm}%`);
    }

    // Apply sorting
    productsQuery = this.applyProductSorting(productsQuery, params.productSort);

    // Apply pagination
    productsQuery = productsQuery.range(offset, offset + params.limit - 1);

    const { data, error } = await productsQuery;

    if (error) {
      console.error("Error fetching products:", error);
      return [];
    }

    return this.processProductData(data || []);
  }

  /**
   * Apply filters to business queries
   */
  private applyBusinessFilters(
    countQuery: any,
    businessQuery: any,
    params: NormalizedParams
  ): [any, any] {
    // Search term filter
    if (params.searchTerm) {
      countQuery = countQuery.ilike("business_name", `%${params.searchTerm}%`);
      businessQuery = businessQuery.ilike(
        "business_name",
        `%${params.searchTerm}%`
      );
    }

    // Location filters
    if (params.city) {
      countQuery = countQuery.eq("city", params.city);
      businessQuery = businessQuery.eq("city", params.city);
    }
    if (params.pincode) {
      countQuery = countQuery.eq("pincode", params.pincode);
      businessQuery = businessQuery.eq("pincode", params.pincode);
    }
    if (params.locality) {
      countQuery = countQuery.eq("locality", params.locality);
      businessQuery = businessQuery.eq("locality", params.locality);
    }

    // Category filter - crucial for category filtering
    if (params.category) {
      countQuery = countQuery.eq("business_category", params.category);
      businessQuery = businessQuery.eq("business_category", params.category);
    }

    return [countQuery, businessQuery];
  }

  /**
   * Apply sorting to product queries
   */
  private applyProductSorting(query: any, sortBy: ProductSortOption): any {
    switch (sortBy) {
      case "newest":
        return query.order("created_at", { ascending: false });
      case "name_asc":
        return query.order("name", { ascending: true });
      case "name_desc":
        return query.order("name", { ascending: false });
      case "price_low":
        return query.order("base_price", { ascending: true });
      case "price_high":
        return query.order("base_price", { ascending: false });
      default:
        return query.order("created_at", { ascending: false });
    }
  }

  /**
   * Process raw business data into BusinessCardData format
   */
  private processBusinessData(rawData: any[]): BusinessCardData[] {
    return rawData.map((data) => ({
      id: data.id,
      business_name: data.business_name ?? "",
      contact_email: data.contact_email ?? "",
      has_active_subscription: true, // Simplified for React Native
      trial_end_date: data.trial_end_date ?? null,
      created_at: data.created_at ?? undefined,
      updated_at: data.updated_at ?? undefined,
      logo_url: data.logo_url ?? "",
      member_name: data.member_name ?? "",
      title: data.title ?? "",
      address_line: data.address_line ?? "",
      city: data.city ?? "",
      state: data.state ?? "",
      pincode: data.pincode ?? "",
      locality: data.locality ?? "",
      phone: data.phone ?? "",
      instagram_url: data.instagram_url ?? "",
      facebook_url: data.facebook_url ?? "",
      whatsapp_number: data.whatsapp_number ?? "",
      about_bio: data.about_bio ?? "",
      status: data.status ?? "online",
      business_slug: data.business_slug ?? "",
      theme_color: data.theme_color ?? "",
      delivery_info: data.delivery_info ?? "",
      business_hours: data.business_hours ?? "",
      business_category: data.business_category ?? "",
      google_maps_url: data.google_maps_url ?? null,
      total_likes: data.total_likes ?? 0,
      total_subscriptions: data.total_subscriptions ?? 0,
      average_rating: data.average_rating ?? 0,
      established_year: data.established_year ?? null,
      website_url: "",
      linkedin_url: "",
      twitter_url: "",
      youtube_url: "",
      call_number: "",
      total_visits: 0,
      today_visits: 0,
      yesterday_visits: 0,
      visits_7_days: 0,
      visits_30_days: 0,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      gallery: null,
      latitude: data.latitude ?? null,
      longitude: data.longitude ?? null,
      custom_branding: data.custom_branding ?? null,
      custom_ads: data.custom_ads ?? null,
    }));
  }

  /**
   * Process raw product data into NearbyProduct format
   */
  private processProductData(rawData: any[]): NearbyProduct[] {
    return rawData.map((product) => ({
      id: product.id,
      business_id: product.business_id,
      name: product.name || "",
      description: product.description,
      base_price: product.base_price,
      discounted_price: product.discounted_price,
      product_type: product.product_type,
      is_available: product.is_available,
      image_url: product.image_url,
      created_at: product.created_at,
      updated_at: product.updated_at,
      slug: product.slug,
      images: product.images || null,
      featured_image_index: product.featured_image_index || null,
      business_slug: product.business_profiles?.business_slug || "",
      businessLatitude: product.business_profiles?.latitude || null,
      businessLongitude: product.business_profiles?.longitude || null,
    }));
  }

  /**
   * Validate and normalize input parameters
   */
  private validateAndNormalizeParams(
    params: DiscoverySearchParams
  ): NormalizedParams {
    return {
      viewType: params.viewType || "products",
      searchTerm: this.normalizeString(params.searchTerm),
      category: this.normalizeString(params.category),
      pincode: this.normalizeString(params.pincode),
      city: this.normalizeString(params.city),
      locality: this.normalizeString(params.locality),
      page: Math.max(1, params.page || 1),
      limit: Math.min(50, Math.max(1, params.limit || 20)),
      businessSort: params.businessSort || "created_desc",
      productSort: params.productSort || "newest",
      productType: params.productType || null,
    };
  }

  /**
   * Normalize string parameters
   */
  private normalizeString(value: string | null | undefined): string | null {
    if (!value || typeof value !== "string") return null;
    const trimmed = value.trim();
    return trimmed.length > 0 ? trimmed : null;
  }

  /**
   * Log operations for debugging
   */
  private logOperation(operation: string, params: any): void {
    console.log(`DiscoveryService.${operation}:`, {
      viewType: params.viewType,
      searchTerm: params.searchTerm,
      category: params.category,
      location: {
        city: params.city,
        pincode: params.pincode,
        locality: params.locality,
      },
      pagination: {
        page: params.page,
        limit: params.limit,
      },
      sorting: {
        businessSort: params.businessSort,
        productSort: params.productSort,
      },
    });
  }
}

// Export singleton instance
export const discoveryService = new DiscoveryService();
