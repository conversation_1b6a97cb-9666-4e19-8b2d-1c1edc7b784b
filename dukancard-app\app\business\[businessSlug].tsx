import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useTheme } from '@/src/hooks/useTheme';
import { responsiveFontSize } from '@/lib/theme/colors';
import { discoverBusinessForUser } from '@/backend/supabase/services/business/businessDiscovery';
import { BusinessDiscoveryData } from '@/src/types/business';
import { Tables } from '@/src/types/supabase';
import PublicCardView from '@/src/components/business/PublicCardView';
import { LoadingSpinner } from '@/src/components/shared/ui/LoadingSpinner';
import { EmptyState } from '@/src/components/shared/ui/EmptyState';
import { useToast } from '@/src/components/ui/Toast';
import UnifiedBottomNavigation from '@/src/components/shared/navigation/UnifiedBottomNavigation';
import { PublicCardNavigationSkeleton } from '@/src/components/ui/SkeletonLoader';

export default function BusinessCardScreen() {
  const { businessSlug } = useLocalSearchParams<{ businessSlug: string }>();
  const router = useRouter();
  const theme = useTheme();
  const toast = useToast();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [businessData, setBusinessData] = useState<BusinessDiscoveryData | null>(null);

  const backgroundColor = theme.colors.background;
  const textColor = theme.colors.textPrimary;
  const borderColor = theme.colors.border;
  const statusBarStyle = theme.isDark ? 'light' : 'dark';

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    centerContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.md,
    },
    backButton: {
      position: 'absolute',
      top: 50,
      left: 20,
      zIndex: 1000,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
    },
    backButtonIcon: {
      padding: theme.spacing.xs,
      marginLeft: -theme.spacing.xs,
    },
    header: {
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderBottomWidth: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
  });

  const loadBusinessData = useCallback(async () => {
    if (!businessSlug) {
      setError('Invalid business URL');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await discoverBusinessForUser(businessSlug);

      if (result.success && result.data) {
        setBusinessData({ success: true, data: result.data });
      } else {
        setBusinessData({ success: false, error: result.error || 'Failed to load business information' });
        toast.error(result.error || 'Failed to load business information');
      }
    } catch (err) {
      console.error('Error loading business data:', err);
      setError('An unexpected error occurred');
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [businessSlug, toast]);

  useEffect(() => {
    if (businessSlug) {
      loadBusinessData();
    }
  }, [businessSlug, loadBusinessData]);

  const handleBack = () => {
    // Navigate back to the appropriate dashboard
    if (router.canGoBack()) {
      router.back();
    } else {
      // Fallback to main app home instead of assuming user type
      router.replace('/');
    }
  };

  const handleRetry = () => {
    loadBusinessData();
  };



  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor }]}>
        <StatusBar style={statusBarStyle} backgroundColor={backgroundColor} />
        <SafeAreaView style={{ flex: 1 }}>
          <PublicCardNavigationSkeleton />
        </SafeAreaView>
        <UnifiedBottomNavigation showQRScanner={true} />
      </View>
    );
  }

  if (error || !businessData || !businessData.data) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <StatusBar style={statusBarStyle} backgroundColor={backgroundColor} />
        <View style={[styles.header, { backgroundColor, borderBottomColor: borderColor }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
          >
            <ArrowLeft color={textColor} size={24} />
          </TouchableOpacity>
        </View>
        <View style={styles.centerContainer}>
          <EmptyState
            title="Business Not Found"
            description={error || 'This business could not be found or is currently unavailable.'}
            actionText="Try Again"
            onAction={handleRetry}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar style="light" backgroundColor="transparent" translucent />
      <View style={{ flex: 1 }}>
        <PublicCardView businessData={businessData} onClose={handleBack} />
      </View>
      <UnifiedBottomNavigation showQRScanner={true} />
    </View>
  );
}


