I will provide you with files which you need to read, find supabase usage, and refactor them to C:\web-app\dukancard\lib\supabase\services\userService.ts, where all commonly used and duplicate supabase functions are declared. You also need to add comment to each function to define what each function does. This will greatly reduce redundancy and decouple supabsae from my main source code. Also make sure you repalce string column name table name and bucket name with C:\web-app\dukancard\lib\supabase\constants.ts and use centralized types from C:\web-app\dukancard\types\supabase.ts. If the functions do not exist in userService, then add them and refactor the code.

I will provide you with files which you need to read, find supabase usage, and refactor them to C:\web-app\dukancard-app\src\config\supabase\services\userService.ts, where all commonly used and duplicate supabase functions are declared. You also need to add comment to each function to define what each function does. This will greatly reduce redundancy and decouple supabsae from my main source code. Also make sure you repalce string column name table name and bucket name with C:\web-app\dukancard-app\src\config\supabase\constants.ts and use centralized types from C:\web-app\dukancard-app\src\types\supabase.ts. If the functions do not exist in userService, then add them and refactor the code.