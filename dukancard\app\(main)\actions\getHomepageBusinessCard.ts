"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

/**
 * Get the current user's business card data for homepage display
 * Returns null if user is not authenticated or not a business user
 */
export async function getHomepageBusinessCard(): Promise<{
  data?: BusinessCardData;
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      // Not authenticated - this is expected for homepage, so no error
      return { data: undefined };
    }

    // Check if user has a business profile
    const { data: businessProfile, error: profileError } = await supabase
      .from("business_profiles")
      .select(
        `
        id, business_name, contact_email, has_active_subscription,
        trial_end_date, created_at, updated_at, logo_url, member_name, title,
        address_line, city, state, pincode, locality, phone, instagram_url,
        facebook_url, whatsapp_number, about_bio, status, business_slug,
        total_likes, total_subscriptions, average_rating, theme_color,
        delivery_info, business_hours, business_category, total_visits, established_year,
        custom_branding, custom_ads
      `
      )
      .eq("id", user.id)
      .maybeSingle();

    if (profileError) {
      console.error("Error fetching business profile for homepage:", profileError);
      return { error: `Failed to fetch profile: ${profileError.message}` };
    }

    if (!businessProfile) {
      // User is not a business user - this is expected, so no error
      return { data: undefined };
    }

    // Map the data to BusinessCardData format
    const mappedData: BusinessCardData = {
      id: businessProfile.id,
      business_name: businessProfile.business_name ?? "",
      contact_email: businessProfile.contact_email ?? "",
      has_active_subscription: businessProfile.has_active_subscription ?? false,
      trial_end_date: businessProfile.trial_end_date ?? null,
      created_at: businessProfile.created_at ?? undefined,
      updated_at: businessProfile.updated_at ?? undefined,
      logo_url: businessProfile.logo_url ?? "",
      member_name: businessProfile.member_name ?? "",
      title: businessProfile.title ?? "",
      address_line: businessProfile.address_line ?? "",
      city: businessProfile.city ?? "",
      state: businessProfile.state ?? "",
      pincode: businessProfile.pincode ?? "",
      locality: businessProfile.locality ?? "",
      phone: businessProfile.phone ?? "",
      instagram_url: businessProfile.instagram_url ?? "",
      facebook_url: businessProfile.facebook_url ?? "",
      whatsapp_number: businessProfile.whatsapp_number ?? "",
      about_bio: businessProfile.about_bio ?? "",
      theme_color: businessProfile.theme_color ?? "",
      delivery_info: businessProfile.delivery_info ?? "",
      business_hours: businessProfile.business_hours,
      business_category: businessProfile.business_category ?? "",
      
      established_year: businessProfile.established_year ?? null,
      status: (businessProfile.status as "online" | "offline") ?? "offline",
      business_slug: businessProfile.business_slug ?? "",
      total_likes: businessProfile.total_likes ?? 0,
      total_subscriptions: businessProfile.total_subscriptions ?? 0,
      average_rating: businessProfile.average_rating ?? 0,
      total_visits: businessProfile.total_visits ?? 0,
      custom_branding: businessProfile.custom_branding as any ?? undefined,
      custom_ads: businessProfile.custom_ads as any ?? undefined,
    };

    return { data: mappedData };
  } catch (error) {
    console.error("Unexpected error in getHomepageBusinessCard:", error);
    return { error: "An unexpected error occurred" };
  }
}

/**
 * Get user authentication status and type for homepage
 */
export async function getHomepageUserInfo(): Promise<{
  isAuthenticated: boolean;
  userType: "business" | "customer" | null;
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { isAuthenticated: false, userType: null };
    }

    // Check user type by looking at both profile tables
    const [customerRes, businessRes] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id")
        .eq("id", user.id)
        .maybeSingle(),
      supabase
        .from("business_profiles")
        .select("id")
        .eq("id", user.id)
        .maybeSingle(),
    ]);

    if (customerRes.error || businessRes.error) {
      console.error("Error checking user type:", customerRes.error || businessRes.error);
      return {
        isAuthenticated: true,
        userType: null,
        error: "Error determining user type"
      };
    }

    let userType: "business" | "customer" | null = null;
    if (customerRes.data) {
      userType = "customer";
    } else if (businessRes.data) {
      userType = "business";
    }

    return { isAuthenticated: true, userType };
  } catch (error) {
    console.error("Unexpected error in getHomepageUserInfo:", error);
    return {
      isAuthenticated: false,
      userType: null,
      error: "An unexpected error occurred"
    };
  }
}
