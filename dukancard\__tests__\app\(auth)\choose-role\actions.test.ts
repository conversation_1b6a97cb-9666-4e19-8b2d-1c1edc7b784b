import { createCustomerProfileAction } from '@/app/(auth)/choose-role/actions';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { revalidatePath } from 'next/cache';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => {
  const mockMaybeSingle = jest.fn(() => ({ data: null, error: null }));
  const mockEq = jest.fn(() => ({
    maybeSingle: mockMaybeSingle,
  }));
  const mockSelect = jest.fn(() => ({
    eq: mockEq,
  }));
  const mockInsert = jest.fn(() => ({ data: null, error: null }));

  const mockFrom = jest.fn(() => ({
    select: mockSelect,
    insert: mockInsert,
  }));

  const mockGetUser = jest.fn(() => ({ data: { user: null } }));

  return {
    createClient: jest.fn(() => ({
      auth: {
        getUser: mockGetUser,
      },
      from: mockFrom,
    })),
  };
});
jest.mock('next/navigation', () => ({
  redirect: jest.fn((url: string) => {
    // Simulate redirect by throwing an error that can be caught in tests
    const error = new Error('Mock Redirect');
    (error as any).digest = 'NEXT_REDIRECT'; // Next.js specific property
    throw error;
  }),
}));
jest.mock('next/cache');

describe('createCustomerProfileAction', () => {
  const mockSupabase = createClient as jest.Mock;
  const mockRedirect = redirect as unknown as jest.Mock;
  const mockRevalidatePath = revalidatePath as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if userId is not provided', async () => {
    const result = await createCustomerProfileAction('');
    expect(result).toEqual({ error: 'User ID is required.' });
    expect(mockSupabase).not.toHaveBeenCalled();
    expect(mockRedirect).not.toHaveBeenCalled();
  });

  it('should return an error if Supabase user cannot be fetched', async () => {
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: null }, error: { message: 'Auth error' } });

    const result = await createCustomerProfileAction('user-123');
    expect(result).toEqual({ error: 'User not found or authentication error.' });
    expect(mockRedirect).not.toHaveBeenCalled();
  });

  it('should redirect to redirectSlug if customer profile already exists', async () => {
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: { id: 'user-123' } } });
    mockSupabase().from('customer_profiles').select().eq('id', 'user-123').maybeSingle.mockResolvedValue({ data: { id: 'user-123' }, error: null });

    try {
      await createCustomerProfileAction('user-123', 'some-card-slug');
    } catch (e: unknown) {
      expect((e as Error).message).toBe('Mock Redirect');
    }
    expect(mockRedirect).toHaveBeenCalledWith('/some-card-slug');
    expect(mockRevalidatePath).not.toHaveBeenCalled(); // Redirect happens before revalidate
  });

  it('should redirect to customer dashboard if profile exists and no redirectSlug', async () => {
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: { id: 'user-123' } } });
    mockSupabase().from('customer_profiles').select().eq('id', 'user-123').maybeSingle.mockResolvedValue({ data: { id: 'user-123' }, error: null });

    try {
        await createCustomerProfileAction('user-123');
    } catch (e: unknown) {
      expect((e as Error).message).toBe('Mock Redirect');
    }
    expect(mockRedirect).toHaveBeenCalledWith('/dashboard/customer');
    expect(mockRevalidatePath).not.toHaveBeenCalled();
  });

  it('should successfully create a new customer profile and redirect to dashboard', async () => {
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: { id: 'user-123', email: '<EMAIL>', user_metadata: { full_name: 'Test User' } } } });
    mockSupabase().from('customer_profiles').select().eq('id', 'user-123').maybeSingle.mockResolvedValue({ data: null, error: null }); // No existing profile
    mockSupabase().from('customer_profiles').insert.mockResolvedValue({ error: null });

    try {
        await createCustomerProfileAction('user-123');
    } catch (e: unknown) {
      expect((e as Error).message).toBe('Mock Redirect');
    }

    expect(mockSupabase).toHaveBeenCalled();
    expect(mockSupabase().from).toHaveBeenCalledWith('customer_profiles');
    expect(mockSupabase().from().insert).toHaveBeenCalledWith({
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
    });
    expect(mockRevalidatePath).toHaveBeenCalledWith('/choose-role');
    expect(mockRevalidatePath).toHaveBeenCalledWith('/dashboard/customer');
    expect(mockRedirect).toHaveBeenCalledWith('/dashboard/customer');
  });

  it('should successfully create a new customer profile and redirect to provided slug', async () => {
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: { id: 'user-123', email: '<EMAIL>' } } });
    mockSupabase().from('customer_profiles').select().eq('id', 'user-123').maybeSingle.mockResolvedValue({ data: null, error: null }); // No existing profile
    mockSupabase().from('customer_profiles').insert.mockResolvedValue({ error: null });

    try {
      await createCustomerProfileAction('user-123', 'another-slug');
    } catch (e: unknown) {
      expect((e as Error).message).toBe('Mock Redirect');
    }

    expect(mockSupabase().from('customer_profiles').insert).toHaveBeenCalledWith({
      id: 'user-123',
      name: null,
      email: '<EMAIL>',
    });
    expect(mockRevalidatePath).toHaveBeenCalledWith('/choose-role');
    expect(mockRevalidatePath).toHaveBeenCalledWith('/dashboard/customer');
    expect(mockRedirect).toHaveBeenCalledWith('/another-slug');
  });

  

  it('should return an error if database error occurs when checking profile', async () => {
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: { id: 'user-123' } } });
    mockSupabase().from().select().eq().maybeSingle.mockResolvedValue({ data: null, error: { message: 'DB check error' } });

    const result = await createCustomerProfileAction('user-123');
    expect(result).toEqual({ error: 'Database error checking profile.' });
    expect(mockRedirect).not.toHaveBeenCalled();
  });
});