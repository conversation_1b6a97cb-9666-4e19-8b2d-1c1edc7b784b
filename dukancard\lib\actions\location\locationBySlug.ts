"use server";

import { createClient } from "@/utils/supabase/server";

export type LocationData = {
  Pincode: string;
  OfficeName: string;
  DivisionName: string;
  District: string;
  StateName: string;
  slug: string;
  locality_slug?: string;
};

/**
 * Find a location by its slug without fetching all pincodes
 * This is much more efficient than using getAllPincodes() and filtering
 *
 * @param locationSlug The URL slug of the location to find
 * @returns The location data if found, or null if not found
 */
export async function getLocationBySlug(locationSlug: string): Promise<{
  data?: LocationData;
  error?: string;
}> {
  if (!locationSlug || locationSlug.trim() === '') {
    return { error: "Location slug is required" };
  }

  try {
    const supabase = await createClient();

    // Now we can directly query using the locality_slug column
    const { data, error } = await supabase
      .from("pincodes")
      .select("Pincode, OfficeName, DivisionName, District, StateName, locality_slug")
      .eq("locality_slug", locationSlug)
      .limit(1);

    if (error) {
      console.error("Error fetching location by slug:", error);
      return { error: "Database error fetching location data" };
    }

    if (!data || data.length === 0) {
      return { error: "Location not found" };
    }

    const locationData = data[0];

    return {
      data: {
        ...locationData,
        slug: locationSlug,
        locality_slug: locationData.locality_slug || undefined
      }
    };
  } catch (error) {
    console.error("Error in getLocationBySlug:", error);
    return { error: "An unexpected error occurred" };
  }
}
