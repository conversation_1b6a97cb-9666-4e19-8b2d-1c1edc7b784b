import * as ImagePicker from "expo-image-picker";
import { BUCKETS, TABLES, COLUMNS } from "../constants";
import { supabase } from "@/src/config/supabase";
import { Database, Tables } from "@/src/types/supabase";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { GOOGLE_WEB_CLIENT_ID } from "@/lib/config/google";

type SupabaseClient = typeof supabase;

/**
 * Opens the device camera to capture an image for avatar upload.
 * @returns Promise<ImagePicker.ImagePickerResult> - The result from the image picker.
 */
export const openCameraForAvatar = async (): Promise<ImagePicker.ImagePickerResult> => {
  const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
  if (permissionResult.granted === false) {
    throw new Error("Permission to access camera is required!");
  }
  return await ImagePicker.launchCameraAsync({
    allowsEditing: true,
    aspect: [1, 1],
    quality: 0.5,
  });
};

/**
 * Opens the device gallery to select an image for avatar upload.
 * @returns Promise<ImagePicker.ImagePickerResult> - The result from the image picker.
 */
export const openGalleryForAvatar = async (): Promise<ImagePicker.ImagePickerResult> => {
  const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
  if (permissionResult.granted === false) {
    throw new Error("Permission to access media library is required!");
  }
  return await ImagePicker.launchImageLibraryAsync({
    allowsEditing: true,
    aspect: [1, 1],
    quality: 0.5,
  });
};

/**
 * Uploads an avatar image to Supabase storage.
 * @param userId The ID of the user.
 * @param imageUri The URI of the image to upload.
 * @returns Promise<string> - The public URL of the uploaded avatar.
 */
export const uploadAvatar = async (userId: string, imageUri: string): Promise<string> => {
  const response = await fetch(imageUri);
  const blob = await response.blob();
  const fileExt = imageUri.split(".").pop();
  const fileName = `${userId}.${fileExt}`;
  const filePath = `${userId}/${fileName}`;

  const { error: uploadError, data } = await supabase.storage
    .from(BUCKETS.CUSTOMERS)
    .upload(filePath, blob, {
      upsert: true,
    });

  if (uploadError) {
    throw uploadError;
  }

  const { data: publicUrlData } = supabase.storage
    .from(BUCKETS.CUSTOMERS)
    .getPublicUrl(filePath);

  return publicUrlData.publicUrl;
};

/**
 * Updates the user's avatar URL in the customer_profiles table.
 * @param userId The ID of the user.
 * @param avatarUrl The public URL of the uploaded avatar.
 * @returns Promise<void>
 */
export const updateCustomerAvatar = async (userId: string, avatarUrl: string): Promise<void> => {
  const { error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .update({ [COLUMNS.AVATAR_URL]: avatarUrl })
    .eq(COLUMNS.ID, userId);

  if (error) {
    throw error;
  }
};

/**
 * Signs out the current user from Supabase.
 * @returns Promise<void>
 */
export const signOutUser = async (): Promise<void> => {
  const { error } = await supabase.auth.signOut();
  if (error) {
    throw error;
  }
};

/**
 * Gets the currently authenticated user from Supabase.
 * @returns Promise<User | null> - The Supabase user object or null if not authenticated.
 */
export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

/**
 * Fetches the customer profile for a given user ID.
 * @param userId The ID of the user.
 * @returns Promise<{ data: Tables<'customer_profiles'> | null, error: Error | null }> - The customer profile data or an error.
 */
export const getCustomerProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select("*")
    .eq(COLUMNS.ID, userId)
    .single();

  return { data, error };
};

/**
 * Updates the authenticated user's metadata.
 * @param data The data to update.
 * @returns Promise<void>
 */
export const updateAuthUser = async (data: { full_name: string }) => {
  const { error } = await supabase.auth.updateUser({ data });
  if (error) {
    throw error;
  }
};

/**
 * Upserts a customer profile.
 * @param profileData The profile data to upsert.
 * @returns Promise<{ data: Tables<'customer_profiles'>[] | null, error: Error | null }>
 */
export const upsertCustomerProfile = async (profileData: Database['public']['Tables']['customer_profiles']['Insert']) => {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .upsert(profileData)
    .select();

  return { data, error };
};

/**
 * Sends an email OTP for authentication.
 * @param email The email address to send the OTP to.
 * @returns Promise<{ success: boolean; message?: string }> - Success status and an optional message.
 */
export const sendEmailOTP = async (email: string): Promise<{ success: boolean; message?: string }> => {
  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      shouldCreateUser: true,
    },
  });

  if (error) {
    return { success: false, message: error.message };
  }
  return { success: true };
};

/**
 * Verifies an email OTP.
 * @param email The email address.
 * @param token The OTP token.
 * @returns Promise<{ success: boolean; message?: string }> - Success status and an optional message.
 */
export const verifyEmailOTP = async (email: string, token: string): Promise<{ success: boolean; message?: string }> => {
  const { error } = await supabase.auth.verifyOtp({
    email,
    token,
    type: "email",
  });

  if (error) {
    return { success: false, message: error.message };
  }
  return { success: true };
};

/**
 * Signs in with Google using native authentication.
 * @returns Promise<{ success: boolean; message?: string }> - Success status and an optional message.
 */
export const signInWithGoogleNative = async (): Promise<{ success: boolean; message?: string }> => {
  GoogleSignin.configure({
    scopes: ["https://www.googleapis.com/auth/userinfo.profile", "https://www.googleapis.com/auth/userinfo.email"],
    webClientId: GOOGLE_WEB_CLIENT_ID,
    offlineAccess: true,
    forceCodeForRefreshToken: true,
  });

  try {
    await GoogleSignin.hasPlayServices();
    const userInfo = await GoogleSignin.signIn();
    if (!userInfo?.data?.idToken) {
      return { success: false, message: "No ID token from Google Sign-In." };
    }

    const { error } = await supabase.auth.signInWithIdToken({
      provider: "google",
      token: userInfo.data.idToken,
    });

    if (error) {
      return { success: false, message: error.message };
    }
    return { success: true };
  } catch (error: any) {
    if (error.code === "CANCELED") {
      return { success: false, message: "cancelled" };
    }
    return { success: false, message: error.message };
  }
};

/**
 * Signs in with mobile number and password.
 * @param mobile The mobile number.
 * @param password The password.
 * @returns Promise<{ data: { user: User | null }; error: Error | null }> - Supabase auth response.
 */
export const signInWithMobilePassword = async (mobile: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    phone: `+91${mobile}`,
    password,
  });
  return { data, error };
};

/**
 * Fetches subscriptions for a user, including associated business profiles.
 * @param userId The ID of the user.
 * @returns Promise<{ data: { business_profiles: Tables<'business_profiles'> | null }[] | null, error: Error | null }>
 */
export const getSubscriptionsWithBusinessProfiles = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.PAYMENT_SUBSCRIPTIONS)
    .select(
      `
      id,
      business_profiles!inner (
        city_slug,
        state_slug,
        locality_slug,
        pincode
      )
    `
    )
    .eq(COLUMNS.USER_ID, userId)
    .limit(1);

  return { data, error };
};

/**
 * Fetches location data from the customer_profiles table for a given user ID.
 * @param userId The ID of the user.
 * @returns Promise<{ data: Pick<Tables<'customer_profiles'>, 'city_slug' | 'state_slug' | 'locality_slug' | 'pincode'> | null, error: Error | null }>
 */
export const getCustomerProfileLocation = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select('city_slug, state_slug, locality_slug, pincode')
    .eq(COLUMNS.ID, userId)
    .single();

  return { data, error };
};

/**
 * Fetches location data from the business_profiles table for a given user ID.
 * @param userId The ID of the user.
 * @returns Promise<{ data: Pick<Tables<'business_profiles'>, 'city_slug' | 'state_slug' | 'locality_slug' | 'pincode'> | null, error: Error | null }>
 */
export const getBusinessProfileLocation = async (userId: string) => {
  const { data, error } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(`${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}, ${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}`)
    .eq(COLUMNS.ID, userId)
    .single();

  return { data, error };
};

export interface LikeWithProfile {
  id: string;
  business_profiles: Tables<'business_profiles'> | null;
}

export interface LikesResult {
  items: LikeWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetches user likes with pagination and search.
 * @param userId The ID of the user.
 * @param page The page number to fetch.
 * @param limit The number of items per page.
 * @param searchTerm Optional search term to filter by business name.
 * @returns Promise<LikesResult> - The fetched likes, total count, and pagination info.
 */
export const fetchLikes = async (
  userId: string,
  page: number = 1,
  limit: number = 20,
  searchTerm: string = ""
): Promise<LikesResult> => {
  try {
    let query = supabase
      .from(TABLES.LIKES)
      .select(
        `
        ${COLUMNS.ID},
        ${TABLES.BUSINESS_PROFILES}!inner (
          id, business_name, logo_url, address_line, city, state, pincode,
          locality, phone, business_category, status, business_slug,
          total_likes, total_subscriptions, average_rating
        )
      `
      )
      .eq(COLUMNS.USER_ID, userId);

    if (searchTerm && searchTerm.trim()) {
      query = query.ilike(
        `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
        `%${searchTerm.trim()}%`
      );
    }

    let countQuery = supabase
      .from(TABLES.LIKES)
      .select(
        `
        ${COLUMNS.ID},
        ${TABLES.BUSINESS_PROFILES}!inner (
          ${COLUMNS.ID},
          ${COLUMNS.BUSINESS_NAME}
        )
      `,
        { count: "exact", head: true }
      )
      .eq(COLUMNS.USER_ID, userId);

    if (searchTerm && searchTerm.trim()) {
      countQuery = countQuery.ilike(
        `${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`,
        `%${searchTerm.trim()}%`
      );
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      throw new Error(`Failed to get likes count: ${countError.message}`);
    }

    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page,
      };
    }

    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data: likesWithProfiles, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch likes: ${error.message}`);
    }

    const transformedLikes: LikeWithProfile[] = (likesWithProfiles || []).map(
      (like: any) => ({
        id: like.id,
        business_profiles: Array.isArray(like.business_profiles)
          ? like.business_profiles[0]
          : like.business_profiles,
      })
    );

    const hasMore = totalCount > offset + limit;

    return {
      items: transformedLikes,
      totalCount,
      hasMore,
      currentPage: page,
    };
  } catch (error) {
    console.error("Error in fetchLikes:", error);
    throw error;
  }
};

/**
 * Unlikes a business.
 * @param likeId The ID of the like to remove.
 * @returns Promise<void>
 */
export const unlikeBusiness = async (likeId: string): Promise<void> => {
  try {
    const { error } = await supabase.from(TABLES.LIKES).delete().eq(COLUMNS.ID, likeId);

    if (error) {
      throw new Error(`Failed to unlike: ${error.message}`);
    }
  } catch (error) {
    console.error("Error in unlikeBusiness:", error);
    throw error;
  }
};