import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import ReviewsTab from '@/src/components/business/ReviewsTab';
import { BusinessReview, ReviewSortOption } from '@/backend/supabase/services/business/businessCardDataService';
import { BusinessDiscoveryData } from '@/src/types/business';
import { View, Text, TouchableOpacity, Modal, FlatList, Image } from 'react-native';
import { useRouter } from 'expo-router';

// Mock external components and modules
jest.mock('@/src/components/ui/AnimatedLoader', () => ({
  AnimatedLoader: 'AnimatedLoader',
}));
jest.mock('@/styles/PublicCardViewStyles', () => ({
  createPublicCardViewStyles: jest.fn(() => ({
    section: {},
    reviewStatsContainer: {},
    ratingOverview: {},
    averageRating: {},
    starsContainer: {},
    totalReviews: {},
    sortContainer: {},
    sortButton: {},
    sortButtonText: {},
    flatListContent: {},
    reviewCard: {},
    businessReviewCard: {},
    reviewHeader: {},
    reviewerInfo: {},
    reviewerAvatar: {},
    businessReviewerAvatar: {},
    reviewerAvatarImage: {},
    reviewerDetails: {},
    businessReviewerNameContainer: {},
    reviewerName: {},
    businessReviewerName: {},
    businessBadge: {},
    businessBadgeText: {},
    customerReviewerNameContainer: {},
    customerBadge: {},
    customerBadgeText: {},
    reviewStars: {},
    reviewDate: {},
    reviewText: {},
    infiniteScrollTrigger: {},
    loadMoreContainer: {},
    loadMoreText: {},
    emptyText: {},
    modalOverlay: { testID: 'modal-overlay' },
    sortModal: {},
    sortModalTitle: {},
    sortSection: {},
    sortSectionTitle: {},
    sortOption: {},
    sortOptionSelected: {},
    sortOptionText: {},
    sortOptionTextSelected: {},
  })),
}));
jest.mock('@/src/components/ui/ReviewSkeleton', () => ({
  ReviewListSkeleton: 'ReviewListSkeleton',
}));
jest.mock('expo-router');

// Mock FlatList to allow triggering onEndReached
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    FlatList: jest.fn((props) => {
      return (
        <RN.ScrollView
          testID="mock-FlatList-scroll-view"
          onScrollEndDrag={() => {
            if (props.onEndReached) {
              props.onEndReached();
            }
          }}
        >
          {props.renderItem ? props.data.map((item: any, index: number) => props.renderItem({ item, index })) : null}
          {props.ListFooterComponent && props.ListFooterComponent()}
        </RN.ScrollView>
      );
    }),
    // Mock Image and other components to add testIDs or simplify
    Image: jest.fn((props) => <RN.Image {...props} testID="reviewer-avatar-image" />),
    View: jest.fn((props) => {
      if (props.testID === 'reviewer-avatar') {
        return <RN.View {...props} testID="reviewer-avatar" />;
      }
      if (props.testID === 'user-icon') {
        return <RN.View {...props} testID="user-icon" />;
      }
      return <RN.View {...props} />;
    }),
    TouchableOpacity: jest.fn((props) => {
      if (props.testID === 'modal-overlay') {
        return <RN.TouchableOpacity {...props} testID="modal-overlay" />;
      }
      return <RN.TouchableOpacity {...props} />;
    }),
    Text: RN.Text,
    Modal: RN.Modal,
  };
});

const mockUseRouter = useRouter as jest.Mock;

const mockReviews: BusinessReview[] = [
  {
    id: '1',
    business_profile_id: 'biz1',
    user_id: 'user1',
    rating: 5,
    review_text: 'Great service!',
    created_at: '2023-01-01T10:00:00Z',
    updated_at: '2023-01-01T10:00:00Z', // Added missing property
    reviewer_type: 'customer',
    customer_name: 'John Doe',
    customer_avatar: 'https://example.com/customer1.jpg',
    reviewer_name: undefined,
    reviewer_avatar: undefined,
    reviewer_slug: undefined,
  },
  {
    id: '2',
    business_profile_id: 'biz1',
    user_id: 'user2',
    rating: 3,
    review_text: 'Average experience.',
    created_at: '2023-01-02T10:00:00Z',
    updated_at: '2023-01-02T10:00:00Z', // Added missing property
    reviewer_type: 'business',
    reviewer_name: 'Another Business',
    reviewer_avatar: 'https://example.com/biz_logo.jpg',
    reviewer_slug: 'another-business',
    customer_name: undefined,
    customer_avatar: undefined,
  },
];

const mockReviewStats = {
  averageRating: 4.0,
  totalReviews: 2,
  ratingDistribution: { '1': 0, '2': 0, '3': 1, '4': 0, '5': 1 }, // Added missing property
};

const defaultProps = {
  reviews: mockReviews,
  reviewStats: mockReviewStats,
  isDark: false,
  loadingMore: false,
  hasMore: false,
  sortBy: 'newest' as ReviewSortOption,
  onLoadMore: jest.fn(),
  onSort: jest.fn(),
  useScrollView: false,
  sortLoading: false,
};

describe('ReviewsTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({ push: jest.fn() });
  });

  it('renders correctly with reviews and stats', () => {
    render(<ReviewsTab {...defaultProps} />);
    expect(screen.getByText('4.0')).toBeTruthy(); // Average rating
    expect(screen.getByText('(4.0 average rating)')).toBeTruthy();
    expect(screen.getByText('Sort by: Newest First')).toBeTruthy();
    expect(screen.getByText('Great service!')).toBeTruthy();
    expect(screen.getByText('Average experience.')).toBeTruthy();
  });

  it('renders empty state when no reviews', () => {
    render(<ReviewsTab {...defaultProps} reviews={[]} reviewStats={null} />);
    expect(screen.getByText('No reviews available')).toBeTruthy();
    expect(screen.queryByText('Sort by: Newest First')).toBeNull();
  });

  it('shows ReviewListSkeleton when sortLoading is true', () => {
    render(<ReviewsTab {...defaultProps} sortLoading={true} />);
    expect(screen.getByText('ReviewListSkeleton')).toBeTruthy();
    expect(screen.queryByText('Great service!')).toBeNull();
  });

  it('shows and hides sort modal', () => {
    render(<ReviewsTab {...defaultProps} />);
    fireEvent.press(screen.getByText('Sort by: Newest First'));
    expect(screen.getByText('Sort Reviews')).toBeTruthy();

    fireEvent.press(screen.getByTestId('modal-overlay'));
    expect(screen.queryByText('Sort Reviews')).toBeNull();
  });

  it('calls onSort with selected option when sort option is pressed', () => {
    const mockOnSort = jest.fn();
    render(<ReviewsTab {...defaultProps} onSort={mockOnSort} />);

    fireEvent.press(screen.getByText('Sort by: Newest First')); // Open modal
    fireEvent.press(screen.getByText('Highest Rating')); // Select sort option

    expect(mockOnSort).toHaveBeenCalledWith('highest_rating');
    expect(screen.queryByText('Sort Reviews')).toBeNull(); // Modal should close
  });

  it('calls onLoadMore when FlatList reaches end and hasMore is true', () => {
    const mockOnLoadMore = jest.fn();
    render(<ReviewsTab {...defaultProps} hasMore={true} onLoadMore={mockOnLoadMore} />);

    fireEvent(screen.getByTestId('mock-FlatList-scroll-view'), 'onEndReached');
    expect(mockOnLoadMore).toHaveBeenCalledTimes(1);
  });

  it('does not call onLoadMore when FlatList reaches end and hasMore is false', () => {
    const mockOnLoadMore = jest.fn();
    render(<ReviewsTab {...defaultProps} hasMore={false} onLoadMore={mockOnLoadMore} />);

    fireEvent(screen.getByTestId('mock-FlatList-scroll-view'), 'onEndReached');
    expect(mockOnLoadMore).not.toHaveBeenCalled();
  });

  it('shows loading indicator in footer when loadingMore and hasMore', () => {
    render(<ReviewsTab {...defaultProps} loadingMore={true} hasMore={true} />);
    expect(screen.getByText('Loading more reviews...')).toBeTruthy();
    expect(screen.getByText('AnimatedLoader')).toBeTruthy();
  });

  it('navigates to business profile when business reviewer is pressed', () => {
    const mockPush = jest.fn();
    mockUseRouter.mockReturnValue({ push: mockPush });
    render(<ReviewsTab {...defaultProps} />);

    fireEvent.press(screen.getByText('Another Business'));
    expect(mockPush).toHaveBeenCalledWith('/business/another-business');
  });

  it('renders customer avatar or User icon for customer reviews', () => {
    render(<ReviewsTab {...defaultProps} />);
    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('Customer')).toBeTruthy();
    expect(screen.getByTestId('reviewer-avatar-image')).toBeTruthy(); // For John Doe's avatar
  });

  it('renders business avatar or Building2 icon for business reviews', () => {
    render(<ReviewsTab {...defaultProps} />);
    expect(screen.getByText('Another Business')).toBeTruthy();
    expect(screen.getByText('Business')).toBeTruthy();
    expect(screen.getByTestId('reviewer-avatar-image')).toBeTruthy(); // For Another Business's logo
  });

  it('displays correct date format', () => {
    render(<ReviewsTab {...defaultProps} />);
    expect(screen.getByText('1/1/2023')).toBeTruthy(); // Assuming default locale format
  });

  it('renders review text if available', () => {
    render(<ReviewsTab {...defaultProps} />);
    expect(screen.getByText('Great service!')).toBeTruthy();
  });

  it('does not render review text if not available', () => {
    const reviewsWithoutText = [{ ...mockReviews[0], review_text: undefined }];
    render(<ReviewsTab {...defaultProps} reviews={reviewsWithoutText} />);
    expect(screen.queryByText('Great service!')).toBeNull();
  });

  it('renders reviews in a View when useScrollView is true', () => {
    render(<ReviewsTab {...defaultProps} useScrollView={true} />);
    expect(screen.getByTestId('mock-FlatList-scroll-view')).toBeTruthy(); // Still uses ScrollView internally due to mock
    // The key is that it doesn't use the FlatList component directly, but maps over the items.
    expect(screen.getByText('Great service!')).toBeTruthy();
  });
});