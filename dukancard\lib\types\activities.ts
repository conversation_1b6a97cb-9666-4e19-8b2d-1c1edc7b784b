import { Tables } from "@/types/supabase";

type BusinessProfilesRow = Tables<'business_profiles'>;
type CustomerProfilesRow = Tables<'customer_profiles'>;

export type ActivityUserProfile =
  | (Partial<BusinessProfilesRow> & {
      is_business: true;
      business_name?: string;
      business_slug?: string;
      logo_url?: string | null;
    })
  | (Partial<CustomerProfilesRow> & {
      is_business: false;
      name?: string | null;
      avatar_url?: string | null;
    });
