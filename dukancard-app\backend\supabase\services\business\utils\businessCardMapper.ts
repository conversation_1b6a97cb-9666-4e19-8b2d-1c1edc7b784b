import { BusinessProfile } from "@/src/types/business";
import { BusinessProfileSelect } from "@/src/types/discovery";

export function mapBusinessCardData(data: BusinessProfile): BusinessProfileSelect {
  return {
    id: data.id,
    business_name: data.business_name,
    member_name: data.member_name,
    title: data.title,
    logo_url: data.logo_url,
    address_line: data.address_line,
    city: data.city,
    city_slug: data.city_slug,
    state: data.state,
    state_slug: data.state_slug,
    pincode: data.pincode,
    locality: data.locality,
    locality_slug: data.locality_slug,
    phone: data.phone,
    instagram_url: data.instagram_url,
    facebook_url: data.facebook_url,
    whatsapp_number: data.whatsapp_number,
    about_bio: data.about_bio,
    status: data.status,
    business_slug: data.business_slug,
    theme_color: data.theme_color,
    delivery_info: data.delivery_info,
    business_hours: data.business_hours,
    contact_email: data.contact_email,
    business_category: data.business_category,
    established_year: data.established_year,
    custom_branding: data.custom_branding,
    custom_ads: data.custom_ads,
    created_at: data.created_at,
    updated_at: data.updated_at,
    latitude: data.latitude,
    longitude: data.longitude,
    average_rating: data.average_rating,
    gallery: data.gallery,
    google_maps_url: data.google_maps_url,
    has_active_subscription: data.has_active_subscription,
    trial_end_date: data.trial_end_date,
    today_visits: data.today_visits,
    total_likes: data.total_likes,
    total_subscriptions: data.total_subscriptions,
    total_visits: data.total_visits,
    visits_30_days: data.visits_30_days,
    visits_7_days: data.visits_7_days,
    yesterday_visits: data.yesterday_visits,
    user_plan: null, // This field is optional and not in the base BusinessProfile
  };
}
