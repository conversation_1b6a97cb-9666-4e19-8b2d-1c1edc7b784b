import { Tables } from "../../types/supabase";


import { BusinessSortBy } from "./types";
import { getSecureBusinessProfiles } from "./utils/secureBusinessProfiles";

// Function to fetch more business cards for infinite scroll
export async function fetchMoreBusinessCardsCombined(params: {
  businessName?: string | null;
  pincode?: string | null;
  locality?: string | null;
  city?: string | null;
  page: number;
  limit?: number;
  sortBy?: BusinessSortBy;
}): Promise<{
  data?: {
    businesses: import("@/src/types/discovery").BusinessCardData[];
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  // Use fetchBusinessesBySearch directly to avoid circular dependency
  console.log("fetchMoreBusinessCardsCombined called with:", params);
  const result = await fetchBusinessesBySearch(params);

  if (result.error) {
    console.error("fetchMoreBusinessCardsCombined error:", result.error);
    return { error: result.error };
  }
  console.log("fetchMoreBusinessCardsCombined returning:", result.data);

  if (!result.data?.businesses) {
    return { error: "No business data found" };
  }

  return {
    data: {
      businesses: result.data.businesses,
      totalCount: result.data.totalCount,
      hasMore: result.data.hasMore,
      nextPage: result.data.nextPage,
    },
  };
}

// Function to fetch businesses by search criteria
export async function fetchBusinessesBySearch(params: {
  businessName?: string | null;
  pincode?: string | null;
  locality?: string | null;
  city?: string | null;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  category?: string | null;
}): Promise<{
  data?: {
    businesses: import("@/src/types/discovery").BusinessCardData[];
    location?: { city: string; state: string } | null;
    isAuthenticated: boolean;
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    businessName,
    pincode,
    locality,
    city,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    category = null,
  } = params;

  // Authentication is handled by auth guards in React Native
  const isAuthenticated = true;

  try {
    console.log("fetchBusinessesBySearch called with:", params);
    // Use the secure method to fetch business profiles
    const {
      data: businessesData,
      count,
      error: businessesError,
    } = await getSecureBusinessProfiles(
      businessName,
      pincode,
      locality,
      city,
      page,
      limit,
      sortBy,
      category
    );

    if (businessesError) {
      console.error("Search Businesses By Name Error:", businessesError);
      return { error: businessesError };
    }
    console.log("fetchBusinessesBySearch getSecureBusinessProfiles result:", {
      businessesData,
      count,
    });

    const totalCount = count || 0;
    // Calculate if there are more pages
    const hasMore =
      totalCount > (page - 1) * limit + (businessesData?.length || 0);
    const nextPage = hasMore ? page + 1 : null;

    // Map raw data to BusinessCardData, handling potential nulls
    const businesses: import("@/src/types/discovery").BusinessCardData[] =
      businessesData?.map((data: any) => {
        // Use the actual data from the database
        return {
          id: data.id,
          business_name: data.business_name ?? "",
          contact_email: "", // Not included in secure data
          has_active_subscription:
            data.subscription_status === "active" ||
            data.subscription_status === "authenticated",
          trial_end_date: data.trial_end_date ?? null,
          created_at: data.created_at ?? undefined,
          updated_at: data.updated_at ?? undefined,
          logo_url: data.logo_url ?? "",
          member_name: data.member_name ?? "",
          title: data.title ?? "",
          address_line: data.address_line ?? "",
          city: data.city ?? "",
          state: data.state ?? "",
          pincode: data.pincode ?? "",
          locality: data.locality ?? "",
          phone: data.phone ?? "",
          business_category: data.business_category ?? "",
          instagram_url: data.instagram_url ?? "",
          facebook_url: data.facebook_url ?? "",
          whatsapp_number: data.whatsapp_number ?? "",
          about_bio: data.about_bio ?? "",
          status: data.status === "online" ? "online" : "offline",
          business_slug: data.business_slug ?? "",

          // Include metrics data
          total_likes: data.total_likes ?? 0,
          total_subscriptions: data.total_subscriptions ?? 0,
          average_rating: data.average_rating ?? 0,

          // Use actual data if available, otherwise use defaults
          theme_color: data.theme_color ?? "#D4AF37",
          delivery_info: data.delivery_info ?? "",
          business_hours: data.business_hours,

          established_year: data.established_year ?? null,

          // Add default values for fields required by BusinessCardData but not in our query
          website_url: "",
          linkedin_url: "",
          twitter_url: "",
          youtube_url: "",
          call_number: "", // This field doesn't exist in the database
          total_visits: 0,
          today_visits: 0,
          yesterday_visits: 0,
          visits_7_days: 0,
          visits_30_days: 0,
          city_slug: null,
          state_slug: null,
          locality_slug: null,
          gallery: null,
          latitude: data.latitude ?? null,
          longitude: data.longitude ?? null,
          custom_branding: null,
          custom_ads: null,
        };
      }) ?? [];

    const result = {
      data: {
        businesses,
        isAuthenticated,
        totalCount,
        hasMore,
        nextPage,
      },
    };
    return result;
  } catch (e) {
    console.error("Search Businesses Exception:", e);
    return { error: "An unexpected error occurred during the search." };
  }
}
