"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { BusinessCardData } from "../schema";
import { validateBusinessCardData } from "../validation/businessCardValidation";
import { checkSubscriptionStatus } from "../data/subscriptionChecker";
import { generateUniqueSlug } from "../slug/slugUtils";
import { processBusinessHours } from "../utils/businessHoursProcessor";
import { uploadThemeHeaderImage, deleteThemeHeaderImage, cleanupOldThemeHeaderImages } from "../actions/themeHeaderActions";

/**
 * Updates business card data with validation and processing
 * @param formData - The business card data to update
 * @returns Success/error response with updated data
 */
export async function updateBusinessCard(
  formData: BusinessCardData
): Promise<{ success: boolean; error?: string; data?: BusinessCardData }> {
  const supabase = await createClient();

  // 1. Validate the incoming data
  const validatedFields = validateBusinessCardData(formData);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return {
      success: false,
      error: "Invalid data provided. Please check the form fields.",
    };
  }

  // 2. Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Auth Error:", authError);
    return { success: false, error: "User not authenticated." };
  }

  // Get existing profile to compare phone numbers
  const { data: existingProfile, error: profileError } = await supabase
    .from("business_profiles")
    .select("phone")
    .eq("id", user.id)
    .single();

  if (profileError) {
    console.error("Profile fetch error:", profileError);
    return { success: false, error: "Failed to fetch existing profile." };
  }

  // 3. Check subscription status if going online
  if (validatedFields.data.status === "online") {
    const subscriptionCheck = await checkSubscriptionStatus(user.id);
    if (!subscriptionCheck.canGoOnline) {
      return {
        success: false,
        error: subscriptionCheck.error || "Cannot set card to online status."
      };
    }
  }

  // 4. Handle Slug Logic if going online
  let finalSlug = validatedFields.data.business_slug;

  if (validatedFields.data.status === "online") {
    const slugResult = await generateUniqueSlug(
      validatedFields.data.business_name,
      finalSlug || "",
      user.id
    );

    if (!slugResult.success) {
      return {
        success: false,
        error: slugResult.error || "Failed to generate unique slug."
      };
    }

    finalSlug = slugResult.slug;
  } else {
    finalSlug = validatedFields.data.business_slug;
  }

  // 5. Handle theme-specific header image uploads
  const updatedCustomBranding = { ...validatedFields.data.custom_branding };

  // Handle light theme header upload
  if (validatedFields.data.custom_branding?.pending_light_header_file) {
    const lightFile = validatedFields.data.custom_branding.pending_light_header_file as File;
    const lightUploadResult = await uploadThemeHeaderImage(lightFile, 'light');

    if (lightUploadResult.success && lightUploadResult.url) {
      // Clean up old light theme images
      if (updatedCustomBranding.custom_header_image_light_url) {
        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_light_url);
      }
      await cleanupOldThemeHeaderImages(user.id, 'light', lightUploadResult.url);

      updatedCustomBranding.custom_header_image_light_url = lightUploadResult.url;
    } else {
      console.error("Light theme header upload failed:", lightUploadResult.error);
      return {
        success: false,
        error: `Failed to upload light theme header: ${lightUploadResult.error}`,
      };
    }
  }

  // Handle dark theme header upload
  if (validatedFields.data.custom_branding?.pending_dark_header_file) {
    const darkFile = validatedFields.data.custom_branding.pending_dark_header_file as File;
    const darkUploadResult = await uploadThemeHeaderImage(darkFile, 'dark');

    if (darkUploadResult.success && darkUploadResult.url) {
      // Clean up old dark theme images
      if (updatedCustomBranding.custom_header_image_dark_url) {
        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_dark_url);
      }
      await cleanupOldThemeHeaderImages(user.id, 'dark', darkUploadResult.url);

      updatedCustomBranding.custom_header_image_dark_url = darkUploadResult.url;
    } else {
      console.error("Dark theme header upload failed:", darkUploadResult.error);
      return {
        success: false,
        error: `Failed to upload dark theme header: ${darkUploadResult.error}`,
      };
    }
  }

  // Handle deletion of theme-specific headers (when URL is empty but no new file)
  if (validatedFields.data.custom_branding?.custom_header_image_light_url === "" &&
      !validatedFields.data.custom_branding?.pending_light_header_file) {
    // Get current light URL from database to delete
    const { data: currentProfile } = await supabase
      .from("business_profiles")
      .select("custom_branding")
      .eq("id", user.id)
      .single();

    if (currentProfile?.custom_branding && typeof currentProfile.custom_branding === 'object' && 'custom_header_image_light_url' in currentProfile.custom_branding && currentProfile.custom_branding.custom_header_image_light_url) {
      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_light_url as string);
    }
    updatedCustomBranding.custom_header_image_light_url = "";
  }

  if (validatedFields.data.custom_branding?.custom_header_image_dark_url === "" &&
      !validatedFields.data.custom_branding?.pending_dark_header_file) {
    // Get current dark URL from database to delete
    const { data: currentProfile } = await supabase
      .from("business_profiles")
      .select("custom_branding")
      .eq("id", user.id)
      .single();

    if (currentProfile?.custom_branding && typeof currentProfile.custom_branding === 'object' && 'custom_header_image_dark_url' in currentProfile.custom_branding && currentProfile.custom_branding.custom_header_image_dark_url) {
      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_dark_url as string);
    }
    updatedCustomBranding.custom_header_image_dark_url = "";
  }

  // Remove pending file fields from the data to be saved to database
  delete updatedCustomBranding.pending_light_header_file;
  delete updatedCustomBranding.pending_dark_header_file;

  // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number

  // 7. Prepare data for Supabase update
  const businessHoursData = processBusinessHours(validatedFields.data.business_hours);

  const dataToUpdate: Partial<BusinessCardData> = {
    business_name: validatedFields.data.business_name,
    member_name: validatedFields.data.member_name,
    title: validatedFields.data.title,
    logo_url: validatedFields.data.logo_url,
    established_year: validatedFields.data.established_year,
    address_line: validatedFields.data.address_line,
    city: validatedFields.data.city,
    state: validatedFields.data.state,
    pincode: validatedFields.data.pincode,
    phone: validatedFields.data.phone,
    delivery_info: validatedFields.data.delivery_info,
    
    instagram_url: validatedFields.data.instagram_url,
    facebook_url: validatedFields.data.facebook_url,
    whatsapp_number: validatedFields.data.whatsapp_number,
    about_bio: validatedFields.data.about_bio,
    locality: validatedFields.data.locality,
    theme_color: validatedFields.data.theme_color,
    business_hours: businessHoursData,
    status: validatedFields.data.status,
    business_slug: finalSlug,
    contact_email: validatedFields.data.contact_email,
    business_category: validatedFields.data.business_category,
    custom_branding: updatedCustomBranding,
    custom_ads: validatedFields.data.custom_ads,
  };

  // 7. Update the business profile in Supabase
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update(dataToUpdate)
    .eq("id", user.id)
    .select(
      `
      id, business_name, member_name, title, logo_url, address_line, city, state, pincode, locality,
      phone, instagram_url, facebook_url, whatsapp_number, about_bio, status, business_slug,
      theme_color, delivery_info, business_hours, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, total_likes, total_subscriptions, average_rating,
      business_category, custom_branding, custom_ads, established_year
    `
    )
    .single();

  if (updateError) {
    console.error("Supabase Update Error:", updateError);
    return {
      success: false,
      error: `Failed to update profile: ${updateError.message}`,
    };
  }

  if (!updatedProfile) {
    return {
      success: false,
      error: "Failed to update profile. Profile not found after update.",
    };
  }

  // 8. Update phone in Supabase auth.users table if phone was changed
  if (
    validatedFields.data.phone &&
    validatedFields.data.phone !== existingProfile.phone
  ) {
    const { error: authUpdateError } = await supabase.auth.updateUser({
      phone: `+91${validatedFields.data.phone}`,
    });

    if (authUpdateError) {
      console.warn('Failed to update auth phone field:', authUpdateError.message);
      // Don't fail the operation for this, just log the warning
      // The business_profiles table is updated successfully
    }
  }

  // 9. Revalidate paths
  revalidatePath("/dashboard/business/card");
  if (dataToUpdate.status === "online" && dataToUpdate.business_slug) {
    revalidatePath(`/(main)/card/${dataToUpdate.business_slug}`, "page");
  }

  // 10. Return success response with the updated data
  return { success: true, data: updatedProfile as BusinessCardData };
}
