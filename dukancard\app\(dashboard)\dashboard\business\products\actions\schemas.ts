import * as z from "zod";

// Base Zod object schema
const baseProductServiceSchemaObject = {
  id: z.string().uuid().optional(),
  business_id: z.string().uuid().optional(),
  product_type: z.enum(["physical", "service"]).default("physical"),
  name: z
    .string()
    .min(1, { message: "Product/Service name is required." })
    .max(100, { message: "Name cannot exceed 100 characters." }),
  description: z
    .string()
    .max(500, { message: "Description cannot exceed 500 characters." })
    .optional()
    .or(z.literal("")),
  base_price: z.coerce
    .number({
      required_error: "Base price is required.",
      invalid_type_error: "Base price must be a number.",
    })
    .positive({ message: "Base price must be positive." }),
  discounted_price: z.coerce
    .number({ invalid_type_error: "Discounted price must be a number." })
    .positive({ message: "Discounted price must be positive." })
    .optional()
    .nullable(),
  is_available: z.boolean().default(true),
  image_url: z
    .string()
    .url({ message: "Invalid image URL format." })
    .optional()
    .nullable(),
  images: z.array(z.string()).optional().nullable(),
  featured_image_index: z.number().int().min(0).optional().nullable(),
  slug: z.string().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
};

// Base object schema without refinement
export const baseProductServiceObjectSchema = z.object(baseProductServiceSchemaObject);

// Schema for ADD operations
export const addProductFormSchema = baseProductServiceObjectSchema
  .omit({
    id: true,
    business_id: true,
    created_at: true,
    updated_at: true,
    image_url: true,
  })
  .refine(
    (data) =>
      !data.base_price ||
      !data.discounted_price ||
      data.discounted_price < data.base_price,
    {
      message: "Discounted price must be less than base price.",
      path: ["discounted_price"],
    }
  );

// Schema for UPDATE operations
export const updateProductFormSchema = baseProductServiceObjectSchema
  .partial()
  .refine(
    (data) =>
      !data.base_price ||
      !data.discounted_price ||
      data.discounted_price < data.base_price,
    {
      message: "Discounted price must be less than base price.",
      path: ["discounted_price"],
    }
  );

// Final type for data returned by actions
export type ProductServiceData = z.infer<typeof baseProductServiceObjectSchema>;

// Product Variants Schemas

// Variant values schema - JSONB object with variant type-value pairs
export const variantValuesSchema = z.record(z.string(), z.string()).refine(
  (data) => Object.keys(data).length > 0,
  { message: "At least one variant type-value pair is required." }
).refine(
  (data) => Object.keys(data).length <= 5,
  { message: "Maximum of 5 variant types allowed per product." }
);

// Base variant schema
const baseVariantSchemaObject = {
  id: z.string().uuid().optional(),
  product_id: z.string().uuid(),
  variant_name: z.string().min(1, { message: "Variant name is required." }).max(100, { message: "Variant name cannot exceed 100 characters." }),
  variant_values: variantValuesSchema,
  base_price: z.coerce
    .number({ invalid_type_error: "Base price must be a number." })
    .positive({ message: "Base price must be positive." })
    .optional()
    .nullable(),
  discounted_price: z.coerce
    .number({ invalid_type_error: "Discounted price must be a number." })
    .positive({ message: "Discounted price must be positive." })
    .optional()
    .nullable(),
  is_available: z.boolean().default(true),
  images: z.array(z.string().url()).optional().nullable().default([]),
  featured_image_index: z.number().int().min(0).optional().nullable(),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
};

// Base variant object schema
export const baseVariantObjectSchema = z.object(baseVariantSchemaObject);

// Schema for adding variants
export const addVariantFormSchema = baseVariantObjectSchema
  .omit({
    id: true,
    created_at: true,
    updated_at: true,
  })
  .refine(
    (data) =>
      !data.base_price ||
      !data.discounted_price ||
      data.discounted_price < data.base_price,
    {
      message: "Discounted price must be less than base price.",
      path: ["discounted_price"],
    }
  );

// Schema for updating variants
export const updateVariantFormSchema = baseVariantObjectSchema
  .partial()
  .refine(
    (data) =>
      !data.base_price ||
      !data.discounted_price ||
      data.discounted_price < data.base_price,
    {
      message: "Discounted price must be less than base price.",
      path: ["discounted_price"],
    }
  );

// Note: variantTypeSchema and variantOptionSchema removed since we use hardcoded predefined variants

// Variant combination generation schema
export const generateVariantCombinationsSchema = z.object({
  product_id: z.string().uuid(),
  variant_types_values: z.record(z.string(), z.array(z.string())).refine(
    (data) => Object.keys(data).length > 0,
    { message: "At least one variant type with values is required." }
  ).refine(
    (data) => Object.keys(data).length <= 5,
    { message: "Maximum of 5 variant types allowed per product." }
  ),
});

// Types for variants
export type VariantData = z.infer<typeof baseVariantObjectSchema>;
export type VariantValuesData = z.infer<typeof variantValuesSchema>;