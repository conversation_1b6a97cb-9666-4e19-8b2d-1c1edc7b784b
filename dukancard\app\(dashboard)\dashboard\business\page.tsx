import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import ModernBusinessFeedList from '@/components/feed/ModernBusinessFeedList';
import { getUnifiedFeedPostsWithAuthors } from '@/lib/actions/posts/unifiedFeed';

export const metadata: Metadata = {
  title: "Feed",
  description: "View your business feed and stay updated",
};

export default async function BusinessDashboardPage() {
  const supabase = await createClient();

  // Check if user is authenticated
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your dashboard');
  }

  // Get the user's business profile
  const { data: businessProfile, error: profileError } = await supabase
    .from('business_profiles')
    .select('city_slug, state_slug, locality_slug, pincode, business_name')
    .eq('id', user.id)
    .single();

  if (profileError) {
    // Handle profile error silently or redirect if needed
  }

  // Get initial posts using the unified feed algorithm
  const initialFeedResult = await getUnifiedFeedPostsWithAuthors({
    filter: 'smart',
    page: 1,
    limit: 10,
    city_slug: businessProfile?.city_slug || undefined,
    state_slug: businessProfile?.state_slug || undefined,
    locality_slug: businessProfile?.locality_slug || undefined,
    pincode: businessProfile?.pincode || undefined
  });

  const posts = initialFeedResult.success ? initialFeedResult.data?.items || [] : [];
  const hasMore = initialFeedResult.success ? initialFeedResult.data?.hasMore || false : false;

  if (!initialFeedResult.success) {
    console.error('Error fetching initial posts:', initialFeedResult.error);
  }

  return (
    <ModernBusinessFeedList
      initialPosts={posts}
      initialTotalCount={0} // Not needed for infinite scroll
      initialHasMore={hasMore}
      initialFilter="smart"
      citySlug={businessProfile?.city_slug || undefined}
      stateSlug={businessProfile?.state_slug || undefined}
      localitySlug={businessProfile?.locality_slug || undefined}
      pincode={businessProfile?.pincode || undefined}
      businessName={businessProfile?.business_name || 'Business Owner'}
    />
  );
}
