import { RazorpayWebhookData, RazorpaySubscription } from "../../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import { SupabaseSubscriptionStatus } from "../../types";
import { createClient } from "@/utils/supabase/server";
import {
  isTerminalStatus,
  SUBSCRIPTION_STATUS,
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";

/**
 * Handle subscription.authenticated event
 *
 * This event is triggered when a subscription is authenticated by the customer.
 *
 * When a user switches plans during trial period:
 * 1. First we open Razorpay modal with new subscription
 * 2. If user proceeds with subscription and we receive authenticated status in webhook
 * 3. We check for any existing authenticated subscriptions for the same business
 * 4. If found, we cancel the previous subscription
 * 5. The webhook for subscription.cancelled will delete the old row
 * 6. Then we create a new row for the new subscription
 *
 * It updates all subscription date fields, including:
 * - subscription_start_date: The start date of the current billing cycle
 * - subscription_expiry_time: The end date of the current billing cycle
 * - subscription_charge_time: The date when the next payment will be charged
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionAuthenticated(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription authenticated: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.authenticated',
      eventId: razorpayEventId || `auth_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    // Reason for createAdminClient: Bypasses RLS to ensure accurate and unrestricted access
    // to subscription data for critical webhook processing, especially for checking
    // terminal states and managing plan changes.
    const supabase = await createClient();

    // Check if this subscription is in a terminal state (cancelled, expired, completed)
    // ENHANCED: Include last_webhook_timestamp for race condition protection
    const { data: existingSubscription, error: checkError } = await supabase
      .from("payment_subscriptions")
      .select("cancelled_at, plan_id, subscription_status, last_webhook_timestamp")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (checkError) {
      console.error(`[RAZORPAY_WEBHOOK] Error checking subscription status for ${subscriptionId}:`, checkError);
      // Continue processing anyway
    } else if (existingSubscription) {

      // ENTERPRISE PROTECTION: Check if subscription was already cancelled before making it authenticated
      // This is the specific check requested to prevent delayed webhooks from overriding cancellation
      // Only apply this protection if there's a cancelled_at timestamp (indicating actual cancellation)
      if (existingSubscription.subscription_status === SUBSCRIPTION_STATUS.TRIAL &&
          existingSubscription.plan_id !== 'free' &&
          existingSubscription.cancelled_at) { // Only reject if actually cancelled

        console.warn(`[CANCELLATION_CHECK] Subscription ${subscriptionId} was already cancelled:`);
        console.warn(`  Current status: ${existingSubscription.subscription_status} (trial indicates cancellation)`);
        console.warn(`  Current plan: ${existingSubscription.plan_id} (preserved from cancellation)`);
        console.warn(`  Authenticated webhook timestamp: ${webhookTimestamp} (${new Date(webhookTimestamp * 1000).toISOString()})`);

        if (existingSubscription.last_webhook_timestamp) {
          const lastWebhookTime = new Date(existingSubscription.last_webhook_timestamp).getTime() / 1000;
          const timeDifference = webhookTimestamp - lastWebhookTime;

          console.warn(`  Last webhook timestamp: ${lastWebhookTime} (${new Date(lastWebhookTime * 1000).toISOString()})`);
          console.warn(`  Time difference: ${timeDifference} seconds`);

          // ENTERPRISE RULE: Reject ANY authenticated webhook if subscription is in cancelled state (trial + non-free plan)
          // This prevents delayed authenticated webhooks from overriding cancellation regardless of timing
          console.warn(`  ENTERPRISE DECISION: Rejecting authenticated webhook to preserve cancellation state`);

          return {
            success: true,
            message: `ENTERPRISE PROTECTION: Authenticated webhook rejected - subscription was already cancelled and is in trial state with plan ${existingSubscription.plan_id}`
          };
        } else {
          // No previous webhook timestamp, but subscription is in cancelled state
          console.warn(`  ENTERPRISE DECISION: Rejecting authenticated webhook - subscription is in cancelled state (no previous webhook)`);

          return {
            success: true,
            message: `ENTERPRISE PROTECTION: Authenticated webhook rejected - subscription is in cancelled trial state`
          };
        }
      }

      // CENTRALIZED LOGIC: Check if subscription is in a terminal state
      // CRITICAL FIX: Don't check cancelled_at alone - only check actual subscription_status
      // Trial users can have cancelled_at but should still be able to authenticate subscriptions
      // Free plan users should not be able to authenticate paid subscriptions
      const isTerminalState = existingSubscription.plan_id === "free" ||
                             isTerminalStatus(existingSubscription.subscription_status);

      if (isTerminalState) {
        console.log(`[RAZORPAY_WEBHOOK] Subscription ${subscriptionId} is in terminal state (plan_id: ${existingSubscription.plan_id}, status: ${existingSubscription.subscription_status}), skipping authentication`);
        return { success: true, message: "Subscription is in terminal state, skipping authentication" };
      }

      // ADDITIONAL CHECK: Allow trial users to authenticate even if they have cancelled_at
      // This is critical for trial-to-paid transitions - but only for fresh trials, not cancelled ones
      if (existingSubscription.subscription_status === SUBSCRIPTION_STATUS.TRIAL && existingSubscription.plan_id === 'free') {
        console.log(`[RAZORPAY_WEBHOOK] Fresh trial user ${subscriptionId} authenticating subscription - this is allowed`);
      }
    }

    // Extract business_profile_id and old_subscription_id from notes
    const businessProfileId = subscription.notes?.business_profile_id ||
                             subscription.notes?.user_id;
    const oldSubscriptionId = subscription.notes?.old_subscription_id;

    // NOTE: Plan switch cancellation logic moved to subscription.activated handler
    // This ensures the old subscription is only cancelled after the new one becomes active
    if (oldSubscriptionId) {
      console.log(`[RAZORPAY_WEBHOOK] Plan switch detected. Old subscription ${oldSubscriptionId} will be cancelled when new subscription becomes active`);
    }

    // Also check for any other existing authenticated subscriptions for this business
    if (businessProfileId) {


      // Check for existing authenticated subscriptions for this business
      console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Checking for existing authenticated subscriptions for business ${businessProfileId} before processing Plan B (${subscriptionId})`);
      const { data: existingSubscriptions, error: findError } = await supabase
        .from('payment_subscriptions')
        .select('razorpay_subscription_id, subscription_status')
        .eq('business_profile_id', businessProfileId)
        .eq('subscription_status', SupabaseSubscriptionStatus._AUTHENTICATED)
        .neq('razorpay_subscription_id', subscriptionId); // Exclude the current subscription

      if (findError) {
        console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error finding existing authenticated subscriptions for business ${businessProfileId}:`, findError);
        // Continue anyway - we still want to update the current subscription
        console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite find error`);
      } else if (existingSubscriptions && existingSubscriptions.length > 0) {
        console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Found ${existingSubscriptions.length} existing authenticated subscription(s) for business ${businessProfileId}.`);
        // Found existing authenticated subscriptions, cancel them


        for (const existingSub of existingSubscriptions) {
          console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Attempting to cancel previously authenticated Plan A: ${existingSub.razorpay_subscription_id}`);

          // Use cancelSubscription directly instead of pauseSubscription
          const { cancelSubscription } = await import("../../../services/subscription");
          const cancelResult = await cancelSubscription(existingSub.razorpay_subscription_id!, false); // false means cancel immediately

          if (!cancelResult.success) {
            console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error cancelling authenticated Plan A (${existingSub.razorpay_subscription_id}) via Razorpay:`, cancelResult.error);
            // Continue anyway - we still want to update the current subscription
            console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite Razorpay cancellation error for Plan A.`);
          } else {
            console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Successfully triggered Razorpay cancellation for Plan A (${existingSub.razorpay_subscription_id}).`);

            // Update the subscription record in the database using centralized status
            const now = new Date().toISOString();

            const { error: updateError } = await supabase
              .from("payment_subscriptions")
              .update({
                subscription_status: SUBSCRIPTION_STATUS.CANCELLED,
                cancellation_requested_at: now,
                cancelled_at: now, // Add cancelled_at timestamp
                updated_at: now
              })
              .eq("razorpay_subscription_id", existingSub.razorpay_subscription_id!);

            if (updateError) {
              console.error(`[RAZORPAY_WEBHOOK_SCENARIO_3] Error updating DB record for cancelled Plan A (${existingSub.razorpay_subscription_id}):`, updateError);
              // Continue anyway - we still want to update the current subscription
              console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Continuing despite DB update error for Plan A.`);
            } else {
              console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Successfully updated DB record for cancelled Plan A (${existingSub.razorpay_subscription_id}).`);
            }
          }
        }
      } else {
        console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] No other existing authenticated subscriptions found for business ${businessProfileId}.`);
      }
    }

    // CENTRALIZED UPDATE: Use webhookProcessor for consistent subscription status updates
    // For authenticated subscriptions, we need to handle start_at specially
    // This is especially important for trial users where start_at is in the future
    const startDate = subscription.start_at
      ? new Date(subscription.start_at * 1000).toISOString()
      : (subscription.current_start ? new Date(subscription.current_start * 1000).toISOString() : null);

    console.log(`[SUBSCRIPTION_AUTHENTICATED] Using start date: ${startDate}, start_at: ${subscription.start_at}, current_start: ${subscription.current_start}`);

    // Try to get the payment method from the most recent payment for this subscription
    let paymentMethod = null; // Default to null for authenticated subscriptions

    try {
      // Get the most recent payment for this subscription
      // First, check if we have a payment ID in the notes
      const lastPaymentId = subscription.notes?.last_payment_id;

      if (lastPaymentId) {
        // Import the getPayment function
        const { getPayment } = await import("../../../services/payment");

        // Fetch the payment details
        const paymentResult = await getPayment(lastPaymentId);

        if (paymentResult.success && paymentResult.data) {
          // Extract the payment method
          paymentMethod = paymentResult.data.method;

        } else {
          console.error(`[RAZORPAY_WEBHOOK] Error fetching payment ${lastPaymentId}:`, paymentResult.error);
        }
      } else {
        // console.log(`[RAZORPAY_WEBHOOK] No last_payment_id found in notes for subscription ${subscriptionId}`);
      }
    } catch (paymentError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching payment method:`, paymentError);
      // Continue with null payment method
    }

    // Fetch existing subscription details to check for trial status and preserve trial_expiry_time
    let existingTrialExpiryTime: string | null = null;
    const { data: currentSubscriptionState, error: trialCheckError } = await supabase
      .from("payment_subscriptions")
      .select("subscription_status, subscription_expiry_time")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (trialCheckError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching current subscription state for ${subscriptionId} during trial check:`, trialCheckError);
      // Decide if this error is critical enough to stop processing
      // For now, we'll log and continue, potentially using Razorpay's current_end
    } else if (currentSubscriptionState && currentSubscriptionState.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {
      existingTrialExpiryTime = currentSubscriptionState.subscription_expiry_time;
      console.log(`[RAZORPAY_WEBHOOK] Subscription ${subscriptionId} is currently in trial. Preserving trial expiry: ${existingTrialExpiryTime}`);
    }

    const additionalUpdateData: Record<string, unknown> = {
      subscription_start_date: startDate,
      subscription_expiry_time: existingTrialExpiryTime // Use existing trial expiry if available, otherwise Razorpay's current_end will be used by RPC if this is null
                                  ?? (subscription.current_end ? new Date(subscription.current_end * 1000).toISOString() : null),
      subscription_charge_time: subscription.charge_at ? new Date(subscription.charge_at * 1000).toISOString() : null,
      razorpay_customer_id: subscription.customer_id || null,
      cancellation_requested_at: null // Clear cancellation request
    };

    if (paymentMethod) {
      additionalUpdateData.last_payment_method = paymentMethod;
    }
    
    // Log the data being sent for update
    console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Updating Plan B subscription ${subscriptionId} with data:`, additionalUpdateData);


    // CRITICAL FIX: Use centralized webhookProcessor for all subscription updates
    // This ensures proper sequence validation and consistent state management
    const updateResult = await webhookProcessor.updateSubscriptionStatus(
      subscriptionId,
      SUBSCRIPTION_STATUS.AUTHENTICATED,
      additionalUpdateData,
      webhookTimestamp // Pass webhook timestamp for sequence validation
    );
    console.log(`[RAZORPAY_WEBHOOK_SCENARIO_3] Result of updating Plan B (${subscriptionId}) in DB:`, updateResult);

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, updateResult.message);
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
    }

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription authenticated:", error);

    // Mark event as failed
    const errorMessage = `Error handling subscription authenticated: ${error instanceof Error ? error.message : String(error)}`;
    if (context) {
      await webhookProcessor.markEventAsFailed(context.eventId, errorMessage);
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}