"use server";

import { createClient } from "@/utils/supabase/server";

import { revalidatePath } from "next/cache";
import { getGalleryImagePath } from "@/lib/utils/storage-paths";
import {
  GalleryImage,
  UploadGalleryImageResponse,
  DeleteGalleryImageResponse,
  GetGalleryImagesResponse
} from "./types";
import { getGalleryLimit, canAddMoreGalleryImages } from "./utils";
import { BUCKETS, COLUMNS, TABLES } from "@/lib/supabase/constants";
import { <PERSON><PERSON> } from "@/types/supabase";

/**
 * Upload a gallery image
 */
export async function uploadGalleryImage(
  formData: FormData
): Promise<UploadGalleryImageResponse> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const userId = user.id;

  // Get the file from the form data
  const file = formData.get("image") as File | null;
  if (!file) {
    return { success: false, error: "No image file provided." };
  }

  // Check file type
  const allowedTypes = ["image/png", "image/jpeg", "image/gif", "image/webp"];
  if (!allowedTypes.includes(file.type)) {
    return { success: false, error: "Invalid file type." };
  }

  // Server-side file size validation (15MB limit)
  if (file.size > 15 * 1024 * 1024) {
    return { success: false, error: "File size must be less than 15MB." };
  }

  // Get the current plan from payment_subscriptions
  const { data: subscriptionData, error: subscriptionError } = await supabase
    .from(TABLES.PAYMENT_SUBSCRIPTIONS)
    .select(COLUMNS.PLAN_ID)
    .eq(COLUMNS.BUSINESS_PROFILE_ID, userId)
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  // Default to free plan if no subscription found
  const planId = subscriptionData?.plan_id || "free";

  // Get current gallery images
  const { data: profileData, error: profileError } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(COLUMNS.GALLERY)
    .eq(COLUMNS.ID, userId)
    .single();

  if (profileError) {
    return { success: false, error: "Failed to fetch business profile." };
  }

  // Parse gallery data
  const gallery = (profileData?.gallery as unknown as GalleryImage[]) || [];
  const currentCount = Array.isArray(gallery) ? gallery.length : 0;

  // Check if user can add more images
  const canAddMore = canAddMoreGalleryImages(planId, currentCount);
  if (!canAddMore) {
    return {
      success: false,
      error: `You have reached the limit of ${getGalleryLimit(planId)} gallery images for your ${planId} plan. Please upgrade your plan to add more images.`
    };
  }

  try {
    const timestamp = new Date().getTime();
    const imagePath = getGalleryImagePath(userId, timestamp);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // Use admin client for storage operations to bypass RLS
    

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await supabase.storage
      .from(BUCKETS.BUSINESS)
      .upload(imagePath, fileBuffer, {
        contentType: file.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Gallery Image Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload gallery image: ${uploadError.message}`,
      };
    }

    // Get the public URL using admin client
    const { data: urlData } = supabase.storage
      .from(BUCKETS.BUSINESS)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    // Create new gallery image object
    const newImage: GalleryImage = {
      id: `gallery_${timestamp}`,
      url: urlData.publicUrl,
      path: imagePath,
      created_at: new Date().toISOString()
    };

    // Update the gallery array in business_profiles
    const updatedGallery = Array.isArray(gallery) ? [...gallery, newImage] : [newImage];

    const { error: updateError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .update({ gallery: updatedGallery as any })
      .eq(COLUMNS.ID, userId);

    if (updateError) {
      console.error("Gallery Update Error:", updateError);

      // Try to clean up the uploaded image if the database update fails
      await supabase.storage
        .from(BUCKETS.BUSINESS)
        .remove([imagePath]);

      return {
        success: false,
        error: `Failed to update gallery: ${updateError.message}`,
      };
    }

    revalidatePath("/dashboard/business/gallery");
    return { success: true, image: newImage };
  } catch (error) {
    console.error("Unexpected error during gallery image upload:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Delete a gallery image
 */
/**
 * Update gallery images order
 */
export async function updateGalleryOrder(
  orderedImages: GalleryImage[]
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const userId = user.id;

  try {
    // Update the gallery array in business_profiles with the new order
    const { error: updateError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .update({ gallery: orderedImages })
      .eq(COLUMNS.ID, userId);

    if (updateError) {
      console.error("Gallery Order Update Error:", updateError);
      return {
        success: false,
        error: `Failed to update gallery order: ${updateError.message}`,
      };
    }

    // Revalidate the gallery page to reflect the new order
    revalidatePath("/dashboard/business/gallery");
    return { success: true };
  } catch (error) {
    console.error("Error updating gallery order:", error);
    return {
      success: false,
      error: "An unexpected error occurred while updating gallery order.",
    };
  }
}

export async function deleteGalleryImage(
  imageId: string
): Promise<DeleteGalleryImageResponse> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Get the current gallery data
    const { data: profileData, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.GALLERY)
      .eq(COLUMNS.ID, user.id)
      .single();

    if (profileError || !profileData) {
      return { success: false, error: "Failed to fetch business profile." };
    }

    // Find the image to delete
    const gallery = (profileData.gallery as GalleryImage[]) || [];
    const imageToDelete = Array.isArray(gallery)
      ? gallery.find(img => img.id === imageId)
      : null;

    if (!imageToDelete) {
      return { success: false, error: "Image not found." };
    }
    

    // Try to delete from storage
    let storageDeleteSuccess = false;
    try {
      const { data: _deleteData, error: storageError } = await supabase.storage
        .from(BUCKETS.BUSINESS)
        .remove([imageToDelete.path]);

      if (storageError) {
        console.error("Storage deletion error:", {
          error: storageError,
          path: imageToDelete.path,
          bucket: BUCKETS.BUSINESS
        });
      } else {
        storageDeleteSuccess = true;
      }
    } catch (error) {
      console.error("Exception during storage deletion:", error);
    }

    // Update the gallery array in business_profiles
    const updatedGallery = Array.isArray(gallery)
      ? gallery.filter(img => img.id !== imageId)
      : [];

    const { error: updateError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .update({ gallery: updatedGallery as any })
      .eq(COLUMNS.ID, user.id);

    if (updateError) {
      return { success: false, error: `Failed to update gallery: ${updateError.message}` };
    }

    revalidatePath("/dashboard/business/gallery");

    // Return success with storage deletion status
    if (storageDeleteSuccess) {
      return { success: true };
    } else {
      return {
        success: true,
        warning: "Image removed from gallery, but storage cleanup may have failed"
      };
    }
  } catch (error) {
    console.error("Unexpected error during gallery image deletion:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Get all gallery images for the current user
 */
export async function getGalleryImages(): Promise<GetGalleryImagesResponse> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { images: [], error: "User not authenticated." };
  }

  try {
    const { data: profileData, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.GALLERY)
      .eq(COLUMNS.ID, user.id)
      .single();

    if (profileError) {
      return { images: [], error: `Failed to fetch business profile: ${profileError.message}` };
    }

    const gallery = (profileData?.gallery as unknown as GalleryImage[]) || [];
    const images = Array.isArray(gallery) ? gallery : [];

    // Return images in the order they are stored (preserves custom order)
    // If no custom order has been set, they will be in upload order
    return { images };
  } catch (error) {
    console.error("Unexpected error fetching gallery images:", error);
    return {
      images: [],
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}
