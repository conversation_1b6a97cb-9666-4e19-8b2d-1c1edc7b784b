import { Database, Tables } from "./supabase";


export type BusinessProfile = Tables<"business_profiles">;
export type BusinessProfileInsert = Database['public']['Tables']['business_profiles']['Insert'];
export type BusinessProfileUpdate = Database['public']['Tables']['business_profiles']['Update'];

export type BusinessProfileSelect = BusinessProfile & {
  user_plan?: string | null;
};

export type BusinessCardData = BusinessProfile & {
  // Add any additional properties that are not in the database
};

export type BusinessDiscoveryData = {
  success: boolean;
  error?: string;
  data?: BusinessProfileSelect | null;
  id?: string;
  business_hours?: any;
};


export type BusinessGalleryImage = {
  id?: string;
  url: string;
  alt?: string;
};