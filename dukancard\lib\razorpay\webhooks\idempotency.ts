import { createClient } from "@/utils/supabase/server";

import { SupabaseClient } from "@supabase/supabase-js";
import { RazorpayWebhookData } from "../types/api";

/**
 * Check if a webhook event has already been processed using the unique Razorpay event ID
 *
 * This function checks the processed_webhook_events table to see if an event
 * with the given Razorpay event ID has already been processed.
 *
 * @param razorpayEventId The unique x-razorpay-event-id from the webhook header
 * @param eventType The type of webhook event (for additional validation)
 * @param payload The webhook payload (for logging purposes)
 * @param supabase Optional Supabase client
 * @returns Whether the event has already been processed
 */
export async function isWebhookEventProcessed(
  razorpayEventId: string,
  _eventType: string,
  _payload: RazorpayWebhookData,
  _supabase?: SupabaseClient // Not used, we use admin client
): Promise<boolean> {
  try {
    // Use admin client to bypass RLS for webhook processing
    const client = await createClient();



    // Check if event has already been processed using the unique Razorpay event ID
    const { data, error } = await client
      .from('processed_webhook_events')
      .select('event_id, event_type, status, processed_at')
      .eq('event_id', razorpayEventId)
      .maybeSingle();

    if (error) {
      console.error('[RAZORPAY_WEBHOOK_IDEMPOTENCY] Error checking processed events:', error);
      // In case of error, assume event hasn't been processed to avoid missing events
      return false;
    }

    if (data) {
      console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Event ${razorpayEventId} already processed at ${data.processed_at} with status ${data.status}`);
      return true;
    }

    console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Event ${razorpayEventId} not found in processed events - allowing processing`);
    return false;
  } catch (error) {
    console.error('[RAZORPAY_WEBHOOK_IDEMPOTENCY] Exception checking processed events:', error);
    // In case of exception, assume event hasn't been processed to avoid missing events
    return false;
  }
}

/**
 * Mark a webhook event as processed using the unique Razorpay event ID
 *
 * This function inserts a record into the processed_webhook_events table
 * to mark an event as processed, using the unique x-razorpay-event-id.
 *
 * @param razorpayEventId The unique x-razorpay-event-id from the webhook header
 * @param eventType The type of webhook event
 * @param payload The webhook payload
 * @param status The status of the event processing (default: 'processed')
 * @param errorMessage Optional error message if processing failed
 * @param entityId Optional entity ID (subscription_id, payment_id, etc.) for tracking
 * @param supabase Optional Supabase client
 * @returns Whether the event was successfully marked as processed
 */
export async function markWebhookEventAsProcessed(
  razorpayEventId: string,
  eventType: string,
  payload: RazorpayWebhookData,
  status: string = 'processed',
  errorMessage?: string,
  entityId?: string,
  _supabase?: SupabaseClient // Renamed to _supabase to indicate it's not used
): Promise<boolean> {
  try {
    // Use admin client to bypass RLS
    const supabase = await createClient();



    // Extract entity information for better tracking
    let entityType: string | null = null;
    let extractedEntityId: string | null = entityId || null;

    if (eventType.startsWith('subscription.')) {
      entityType = 'subscription';
      extractedEntityId = extractedEntityId || payload.payload.subscription?.id || null;
    } else if (eventType.startsWith('payment.')) {
      entityType = 'payment';
      extractedEntityId = extractedEntityId || payload.payload.payment?.entity?.id || null;
    } else if (eventType.startsWith('refund.')) {
      entityType = 'refund';
      extractedEntityId = extractedEntityId || payload.payload.refund?.entity?.id || null;
    } else if (eventType.startsWith('invoice.')) {
      entityType = 'invoice';
      extractedEntityId = extractedEntityId || payload.payload.payment?.entity?.id || null;
    }

    // Upsert record into processed_webhook_events table (handles duplicates gracefully)
    const { error } = await supabase
      .from('processed_webhook_events')
      .upsert({
        event_id: razorpayEventId,
        event_type: eventType,
        entity_type: entityType,
        entity_id: extractedEntityId,
        payload: {
          ...payload,
          // Add metadata for tracking
          _metadata: {
            entity_type: entityType,
            entity_id: extractedEntityId,
            processed_timestamp: new Date().toISOString()
          }
        } as any,
        status,
        error_message: errorMessage,
        processed_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
      }, {
        onConflict: 'event_id'
      });

    if (error) {
      console.error('[RAZORPAY_WEBHOOK_IDEMPOTENCY] Error marking event as processed:', error);
      return false;
    }

    console.log(`[RAZORPAY_WEBHOOK_IDEMPOTENCY] Successfully marked event ${razorpayEventId} as ${status}`);
    return true;
  } catch (error) {
    console.error('[RAZORPAY_WEBHOOK_IDEMPOTENCY] Exception marking event as processed:', error);
    return false;
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use the new functions with razorpayEventId instead
 */
export async function isWebhookEventProcessedLegacy(
  entityId: string,
  eventType: string,
  _payload: RazorpayWebhookData,
  supabase?: SupabaseClient
): Promise<boolean> {
  console.warn('[RAZORPAY_WEBHOOK_IDEMPOTENCY] Using legacy idempotency check - this should be migrated to use razorpayEventId');

  try {
    const client = supabase || await createClient();

    // Check using the old method for backward compatibility
    const { data, error } = await client
      .from('processed_webhook_events')
      .select('event_id')
      .eq('event_id', entityId)
      .eq('event_type', eventType)
      .maybeSingle();

    if (error) {
      console.error('[RAZORPAY_WEBHOOK_IDEMPOTENCY] Error in legacy check:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('[RAZORPAY_WEBHOOK_IDEMPOTENCY] Exception in legacy check:', error);
    return false;
  }
}
