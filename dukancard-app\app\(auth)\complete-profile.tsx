import { cleanAddressData } from "@/backend/supabase/utils/addressValidation";
import { router } from "expo-router";
import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { customerProfileCompletionSchema } from "@/src/utils/validationSchemas";
import { useLocationPermission } from "@/src/hooks/useLocationPermission";
import React, { useRef, useState, useEffect } from "react";
import {
  ScrollView,
  View,
  TouchableOpacity,
  Text,
  Alert,
  Animated,
  Easing,
} from "react-native";
import { Loader2, ArrowLeft, LogOut } from "lucide-react-native";
import { responsiveFontSize } from '@/lib/theme/colors';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from "react-native-safe-area-context";
import { useTheme } from "@/src/hooks/useTheme";
import { completeCustomerProfileStyles } from "@/styles/auth/complete-customer-profile-styles";
import {
  openCameraForAvatar,
  openGalleryForAvatar,
  getCurrentUser,
  getCustomerProfile,
  updateAuthUser,
  upsertCustomerProfile,
  uploadAvatar,
  signOutUser
} from "@/src/config/supabase/services/userService";
import { compressImageModerateClient } from "@/src/utils/client-image-compression";

import { usePincodeDetails } from "@/src/hooks/usePincodeDetails";
import { useToast } from "@/src/components/ui/Toast";
import { useAlertDialog } from "@/src/components/providers/AlertProvider";
import { useAuth } from "@/src/contexts/AuthContext";
import NetInfo from "@react-native-community/netinfo";
import { FormData, InitialFormData } from "@/src/types/profile";
import LoadingOverlay from "@/src/components/common/LoadingOverlay";
import * as Location from 'expo-location';

// Import the new step components
import Step1AvatarName from "./components/Step1AvatarName";
import Step2AddressLocation from "./components/Step2AddressLocation";
import ImagePickerBottomSheet,
  {
  ImagePickerBottomSheetRef,
} from "@/src/components/pickers/ImagePickerBottomSheet";
import LocalityBottomSheetPicker,
  {
  LocalityBottomSheetPickerRef,
} from "@/src/components/pickers/LocalityBottomSheetPicker";

/**
 * Complete Customer Profile Screen
 * Dedicated screen for customers only to complete their profile with address details
 * No back button - user must complete profile to proceed
 * Better UX than redirecting to general profile page
 */

const CompleteCustomerProfileScreen = () => {
  const theme = useTheme();
  const { isDark } = theme;
  const toast = useToast();
  const { confirm, error: showError, logout } = useAlertDialog();
  const { refreshProfileStatus } = useAuth();
  const insets = useSafeAreaInsets();
  const styles = completeCustomerProfileStyles(theme);

  const [currentStep, setCurrentStep] = useState(1); // Start at Step 1

  const [initialFormData, setInitialFormData] = useState<InitialFormData>({
    name: "",
    address: "",
    pincode: "",
    city: "",
    state: "",
    locality: "",
    latitude: undefined,
    longitude: undefined,
    avatarUri: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [isCompressingImage, setIsCompressingImage] = useState(false);

  const spinValue = useRef(new Animated.Value(0)).current;
  const spinAnimationRef = useRef<Animated.CompositeAnimation | null>(null);

  useEffect(() => {
    if (isLoading) {
      spinValue.setValue(0);
      Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: false, // Set to false for better compatibility with some transforms
        })
      ).start();
    } else {
      spinValue.stopAnimation();
    }
  }, [isLoading, spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  const [gpsDetectedLocality, setGpsDetectedLocality] = useState<string | null>(
    null
  );
  const [hasGpsCoordinates, setHasGpsCoordinates] = useState(false);

  const { permission: locationPermission } = useLocationPermission();

  const formMethods = useForm<FormData>({
    resolver: yupResolver(customerProfileCompletionSchema) as any,
    defaultValues: {
      name: "",
      address: "",
      pincode: "",
      city: "",
      state: "",
      locality: "",
      avatarUri: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const {
    control,
    handleSubmit: handleFormSubmit, // Renamed to avoid conflict with local handleSubmit
    formState,
    formState: { isValid },
    reset: resetForm,
    watch,
    setValue,
    trigger,
  } = formMethods;

  const scrollViewRef = useRef<ScrollView>(null);
  const imagePickerRef = useRef<ImagePickerBottomSheetRef>(null);
  const localityPickerRef = useRef<LocalityBottomSheetPickerRef>(null);

  // Logout handler
  const handleLogout = async () => {
    logout(async () => {
      try {
        await signOutUser();
        router.replace("/(auth)/login");
      } catch (err) {
        showError("Logout Failed", "Failed to logout. Please try again.");
      }
    });
  };

  const {
    isPincodeLoading,
    availableLocalities,
    handlePincodeChange: fetchPincodeDetails,
  } = usePincodeDetails({
    onPincodeChange: (details) => {
      if (details && details.city && details.state) {
        setValue("city", details.city);
        setValue("state", details.state);
        setValue("locality", "");

        setInitialFormData((prev) => ({
          ...prev,
          pincode: watch("pincode"),
          address: watch("address"),
          city: details.city,
          state: details.state,
          locality: "",
          latitude: watch("latitude"),
          longitude: watch("longitude"),
        }));
      }
    },
  });

  useEffect(() => {
    if (gpsDetectedLocality && availableLocalities.length > 0) {
      setTimeout(() => {
        const matchingLocality = availableLocalities.find(
          (locality) =>
            locality.toLowerCase().trim() ===
            gpsDetectedLocality.toLowerCase().trim()
        );

        if (matchingLocality) {
          setValue("locality", matchingLocality);
          setGpsDetectedLocality(null);

          setTimeout(() => {
            trigger();
          }, 100);
        } else {
        }
      }, 50);
    }
  }, [availableLocalities, gpsDetectedLocality, setValue, trigger]);

  const loadExistingProfile = React.useCallback(async () => {
    try {
      const user = await getCurrentUser();
      if (!user) {
        router.replace("/(auth)/login");
        return;
      }

      const { data: profile } = await getCustomerProfile(user.id);
      if (profile) {
        setInitialFormData((prev) => ({
          ...prev,
          name: profile.name || "",
          address: profile.address || "",
          pincode: profile.pincode || "",
          city: profile.city || "",
          state: profile.state || "",
          locality: profile.locality || "",
          latitude: profile.latitude || prev.latitude,
          longitude: profile.longitude || prev.longitude,
          avatarUri: profile.avatar_url || "",
        }));

        if (profile.latitude && profile.longitude) {
          setHasGpsCoordinates(true);
        }

        // If pincode is already present, fetch details automatically
        if (profile.pincode && profile.pincode.length === 6) {
          fetchPincodeDetails(profile.pincode);
          // Set gpsDetectedLocality to trigger pre-selection if locality exists
          if (profile.locality) {
            setGpsDetectedLocality(profile.locality);
          }
        }
      }
    } catch (error) {
      console.error("Error loading profile:", error);
      showError("Error", "Failed to load profile information");
    } finally {
      setIsLoadingProfile(false);
    }
  }, [showError, fetchPincodeDetails]);

  React.useEffect(() => {
    loadExistingProfile();
  }, [loadExistingProfile]);

  useEffect(() => {
    const currentAvatarUri = watch("avatarUri");
    const currentName = watch("name");
    const currentAddress = watch("address");

    const preservedAvatarUri = currentAvatarUri || initialFormData.avatarUri;
    const preservedName = currentName || initialFormData.name;
    const preservedAddress = currentAddress || initialFormData.address;

    resetForm({
      ...initialFormData,
      name: preservedName,
      address: preservedAddress,
      avatarUri: preservedAvatarUri,
    });
  }, [initialFormData, resetForm, watch]);

  // Handle image selection with compression (moved from original file)
  const handleImageSelect = async (imageUri: string) => {
    try {
      setIsCompressingImage(true);
      const user = await getCurrentUser();
      if (!user) {
        router.replace("/(auth)/login");
        return;
      }

      const response = await fetch(imageUri);
      const blob = await response.blob();
      const originalSize = blob.size;

      const compressionResult = await compressImageModerateClient(
        imageUri,
        originalSize,
        {
          targetSizeKB: 45,
          maxDimension: 400,
          quality: 0.7,
          format: "webp",
        }
      );

      if (!compressionResult.uri) {
        throw new Error("Compression did not return a valid URI");
      }

      setValue("avatarUri", compressionResult.uri);
    } catch (error) {
      console.error("Image compression failed:", error);
      toast.error(
        "Compression Failed",
        "Failed to compress image. Please try again."
      );
    } finally {
      setIsCompressingImage(false);
    }
  };

  // Main submission function (remains largely the same, but now gathers all data)
  const onSubmit = async (values: FormData) => {
    const networkState = await NetInfo.fetch();
    if (!networkState.isConnected) {
      toast.error(
        "No Internet Connection",
        "Please check your internet connection and try again."
      );
      return;
    }

    setIsLoading(true);
    try {
      const user = await getCurrentUser();
      if (!user) {
        router.replace("/(auth)/login");
        return;
      }

      let avatarUrl = null;
      if (values.avatarUri) {
        try {
          avatarUrl = await uploadAvatar(user.id, values.avatarUri);
        } catch (uploadError) {
          console.error("Error uploading avatar:", uploadError);
          toast.warning(
            "Avatar Upload Failed",
            "Profile saved but avatar upload failed. You can update it later."
          );
        }
      }

      const addressData = cleanAddressData({
        pincode: values.pincode,
        state: values.state,
        city: values.city,
        locality: values.locality,
        address: values.address,
      });

      await updateAuthUser({ full_name: values.name.trim() });

      const profileData = {
        id: user.id,
        ...addressData,
        latitude: values.latitude,
        longitude: values.longitude,
        updated_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        email: user.email || null,
        name: values.name?.trim() || null,
        phone: user.phone || null,
        ...(avatarUrl && { avatar_url: avatarUrl }),
      };

      const { data, error } = await upsertCustomerProfile(profileData);

      if (error) {
        console.error("Error updating profile:", error);
        toast.error(
          "Profile Update Failed",
          "Failed to update profile. Please try again."
        );
        return;
      }

      if (!data || data.length === 0) {
        toast.error(
          "Profile Update Failed",
          "Profile update was not confirmed. Please try again."
        );
        return;
      }

      toast.success(
        "Profile Completed!",
        "Your profile has been completed successfully!"
      );

      await new Promise((resolve) => setTimeout(resolve, 1000));

      await refreshProfileStatus();
    } catch (error) {
      console.error("Error submitting profile:", error);

      const networkState = await NetInfo.fetch();
      if (!networkState.isConnected) {
        toast.error(
          "Connection Lost",
          "Your internet connection was lost. Please try again when connected."
        );
      } else {
        toast.error(
          "Unexpected Error",
          "An unexpected error occurred. Please try again."
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const borderColor = isDark ? "#374151" : "#E5E7EB";
  const goldColor = "#D4AF37";

  if (isLoadingProfile) {
    return (
      <LoadingOverlay textColor={textColor} backgroundColor={backgroundColor} />
    );
  }

  const totalSteps = 2; // We have 2 main steps

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      {/* Custom Header */}
      <View style={[styles.customHeader, { backgroundColor, borderBottomColor: borderColor }]}>
        {/* Left Side - Back Button (only for step 2) */}
        <View style={styles.headerLeft}>
          {currentStep > 1 && (
            <TouchableOpacity
              style={styles.headerBackButton}
              onPress={() => setCurrentStep(currentStep - 1)}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              <ArrowLeft size={24} color={textColor} />
            </TouchableOpacity>
          )}
        </View>

        {/* Center - Logo */}
        <View style={styles.headerCenter}>
          <Text style={[styles.headerLogo, { color: goldColor }]}>
            Dukan<Text style={{ color: textColor }}>card</Text>
          </Text>
        </View>

        {/* Right Side - Logout Button */}
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={[styles.headerLogoutButton, { borderColor: goldColor + "40" }]}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <LogOut size={18} color={goldColor} />
          </TouchableOpacity>
        </View>
      </View>

        <View style={styles.contentContainer}>
          <ScrollView
            ref={scrollViewRef}
            style={styles.scrollView}
            contentContainerStyle={[
              styles.scrollContent,
              { paddingBottom: styles.scrollContent.paddingBottom + styles.footer.height + Math.max(insets.bottom, 12) }
            ]}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            nestedScrollEnabled={true}
          >
          <FormProvider {...formMethods}>
            {currentStep === 1 && (
              <Step1AvatarName
                isCompressingImage={isCompressingImage}
                handleImageSelect={handleImageSelect}
                theme={theme}
                styles={styles}
                textColor={textColor}
                imagePickerRef={imagePickerRef}
              />
            )}
            {currentStep === 2 && (
              <Step2AddressLocation
                isPincodeLoading={isPincodeLoading}
                availableLocalities={availableLocalities}
                locationPermission={locationPermission}
                hasGpsCoordinates={hasGpsCoordinates}
                fetchPincodeDetails={fetchPincodeDetails}
                setGpsDetectedLocality={setGpsDetectedLocality}
                handleLocationDetected={(lat, long) => {
                  setValue("latitude", lat);
                  setValue("longitude", long);
                  setHasGpsCoordinates(true);
                  trigger(["latitude", "longitude"]);
                }}
                handleAddressDetected={(address) => {
                  setValue("pincode", address.pincode);
                  setValue("city", address.city);
                  setValue("state", address.state);
                  setGpsDetectedLocality(address.locality);
                  fetchPincodeDetails(address.pincode);
                  setTimeout(() => {
                    trigger();
                  }, 200);
                }}
                handleLocalitySelect={(locality) => {
                  setValue("locality", locality);
                  setTimeout(() => {
                    trigger();
                  }, 100);
                }}
                handlePincodeInputChange={(value) => {
                  const cleanedPincode = value.replace(/\D/g, "").substring(0, 6);
                  setValue("pincode", cleanedPincode);
                  if (cleanedPincode.length === 6) {
                    fetchPincodeDetails(cleanedPincode);
                  }
                }}
                localityPickerRef={localityPickerRef}
                theme={theme}
                styles={styles}
                textColor={textColor}
                borderColor={borderColor}
              />
            )}
          </FormProvider>
          </ScrollView>

          {/* Submit/Navigation Button */}
          <View
            style={[
              styles.footer,
              {
                borderTopColor: borderColor,
                backgroundColor: backgroundColor,
                paddingBottom: Math.max(insets.bottom, 12),
              },
            ]}
          >
          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: goldColor },
              isLoading && styles.submitButtonDisabled,
            ]}
            onPress={async () => {
              // Step-specific validation
              let fieldsToValidate: (keyof FormData)[] = [];

              if (currentStep === 1) {
                // Step 1: Only validate name (avatar is optional)
                fieldsToValidate = ["name"];
              } else if (currentStep === 2) {
                // Step 2: Validate all address and location fields
                fieldsToValidate = ["pincode", "locality", "city", "state", "latitude", "longitude"];
                // address is optional, so we don't include it in validation

                // Automatically fetch latitude and longitude if location permission is granted
                if (locationPermission && locationPermission.granted) {
                  try {
                    const location = await Location.getCurrentPositionAsync({
                      accuracy: Location.Accuracy.Balanced,
                    });
                    setValue("latitude", location.coords.latitude);
                    setValue("longitude", location.coords.longitude);
                    setHasGpsCoordinates(true);
                    trigger(["latitude", "longitude"]); // Trigger validation for these fields
                  } catch (error) {
                    console.error("Error fetching location automatically:", error);
                    toast.warning(
                      "Location Not Captured",
                      "Could not automatically capture your precise location. Please ensure location services are enabled."
                    );
                    // If location cannot be captured, prevent form submission by marking lat/long as invalid
                    setValue("latitude", 0);
                    setValue("longitude", 0);
                  }
                } else {
                  toast.warning(
                    "Location Permission Required",
                    "Please grant location permission to automatically capture your coordinates."
                  );
                  // If permission not granted, ensure lat/long are undefined to fail validation
                  setValue("latitude", 0);
                  setValue("longitude", 0);
                }
              }

              const isFormValid = await trigger(fieldsToValidate);

              if (isFormValid) {
                if (currentStep < totalSteps) {
                  setCurrentStep(currentStep + 1); // Move to next step
                  scrollViewRef.current?.scrollTo({ y: 0, animated: true }); // Scroll to top
                } else {
                  // Last step, submit the form
                  handleFormSubmit(onSubmit)();
                }
              } else {
                const errors = formState.errors;
                const errorFields = Object.keys(errors).filter(field => fieldsToValidate.includes(field as keyof FormData));
                if (errorFields.length > 0) {
                  const firstError =
                    errors[errorFields[0] as keyof typeof errors];
                  toast.error(
                    "Please fix the following errors:",
                    firstError?.message || `${errorFields[0]} is required`
                  );
                }
              }
            }}
            disabled={isLoading}
          >
            {isLoading ? (
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Animated.View style={{ transform: [{ rotate: spin }] }}>
                  <Loader2 size={responsiveFontSize(32)} color={goldColor} style={{ marginRight: 8 }} />
                </Animated.View>
                <Text style={styles.submitButtonText}>Saving...</Text>
              </View>
            ) : (
              <Text style={styles.submitButtonText}>
                {currentStep < totalSteps ? "Continue" : "Complete Profile"}
              </Text>
            )}
          </TouchableOpacity>
          </View>
        </View>

      {/* Bottom Sheet Pickers - At root level for proper screen-bottom appearance */}
      <ImagePickerBottomSheet
        ref={imagePickerRef}
        onCameraPress={async () => {
          imagePickerRef.current?.dismiss();
          try {
            const result = await openCameraForAvatar();
            if (result && !result.canceled && result.assets && result.assets[0]) {
              const imageUri = result.assets[0].uri;
              await handleImageSelect(imageUri);
            }
          } catch (error) {
            console.error("Camera error:", error);
            toast.error("Error", "Failed to take photo. Please try again.");
          }
        }}
        onGalleryPress={async () => {
          imagePickerRef.current?.dismiss();
          try {
            const result = await openGalleryForAvatar();
            if (result && !result.canceled && result.assets && result.assets[0]) {
              const imageUri = result.assets[0].uri;
              await handleImageSelect(imageUri);
            }
          } catch (error) {
            console.error("Gallery error:", error);
            toast.error("Error", "Failed to select image. Please try again.");
          }
        }}
        title="Select Profile Picture"
        cameraLabel="Take Photo"
        galleryLabel="Choose from Gallery"
      />

      <LocalityBottomSheetPicker
        ref={localityPickerRef}
        localities={availableLocalities}
        selectedLocality={watch("locality")}
        onLocalitySelect={(locality) => {
          setValue("locality", locality);
          setTimeout(() => {
            trigger();
          }, 100);
        }}
      />
    </SafeAreaView>
  );
};

export default CompleteCustomerProfileScreen;