import { supabase } from "../../../../src/config/supabase";
import { BusinessDiscoveryData, BusinessProfile, BusinessProfileUpdate } from "@/src/types/business";
import { <PERSON><PERSON> } from "@/src/types/supabase";
import { validateBusinessCardData } from "./schemas";
import { mapBusinessCardData } from "./utils/businessCardMapper";
import { checkForceOfflineStatus } from "./utils/subscriptionChecker";
import { generateUniqueSlug } from "./utils/slugUtils";
import { processBusinessHours } from "./utils/businessHoursProcessor";

/**
 * Fetches business card data for the authenticated user
 * @returns Business card data or error
 */
export async function getBusinessCardData(): Promise<BusinessDiscoveryData> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Select columns including metrics data
  const { data, error } = await supabase
    .from("business_profiles")
    .select(
      `
      id, business_name, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug,
      total_likes, total_subscriptions, average_rating, theme_color, delivery_info, business_hours,
      business_category, custom_branding, custom_ads, established_year, latitude, longitude, google_maps_url
    `
    )
    .eq("id", user.id)
    .single(); // Use single as profile must exist for editing

  if (error) {
    if (error.code === "PGRST116") {
      return { success: false, error: "Business profile not found." };
    }
    console.error("Supabase Fetch Error:", error);
    return { success: false, error: `Failed to fetch profile: ${error.message}` };
  }

  // Server-side check: Force offline if online but subscription is halted or missing required fields
  if (data.status === "online") {
    const subscriptionCheck = await checkForceOfflineStatus(user.id);
    if (subscriptionCheck.shouldForceOffline) {
      console.log(
        `User ${user.id} card forced offline due to ${subscriptionCheck.reason}.`
      );
      const { error: updateError } = await supabase
        .from("business_profiles")
        .update({ status: "offline" })
        .eq("id", user.id);

      if (updateError) {
        console.error("Error forcing card offline:", updateError.message);
      } else {
        data.status = "offline";
      }
    }
  }

  // Map data using the shared mapper
  const mappedData = mapBusinessCardData(data);
  return { success: true, data: mappedData };
}

/**
 * Updates business card data with validation and processing
 * @param formData - The business card data to update
 * @returns Success/error response with updated data
 */
export async function updateBusinessCard(
  formData: BusinessDiscoveryData
): Promise<{ success: boolean; error?: string; data?: BusinessDiscoveryData }> {
  // 1. Validate the incoming data
  const validatedFields = validateBusinessCardData(formData);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return {
      success: false,
      error: "Invalid data provided. Please check the form fields.",
    };
  }

  // 2. Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Auth Error:", authError);
    return { success: false, error: "User not authenticated." };
  }

  // Get existing profile to compare phone numbers
  const { data: existingProfile, error: profileError } = await supabase
    .from("business_profiles")
    .select("phone")
    .eq("id", user.id)
    .single();

  if (profileError) {
    console.error("Profile fetch error:", profileError);
    return { success: false, error: "Failed to fetch existing profile." };
  }

  // 4. Handle Slug Logic if going online
  let finalSlug = validatedFields.data.business_slug;

  if (validatedFields.data.status === "online") {
    const slugResult = await generateUniqueSlug(
      validatedFields.data.business_name,
      finalSlug || "",
      user.id
    );

    if (!slugResult.success) {
      return {
        success: false,
        error: slugResult.error || "Failed to generate unique slug.",
      };
    }

    finalSlug = slugResult.slug!;
  } else {
    finalSlug = validatedFields.data.business_slug;
  }

  // 7. Prepare data for Supabase update
  const businessHoursData = processBusinessHours(
    validatedFields.data.business_hours
  );

  const dataToUpdate: Partial<BusinessProfileUpdate> = {
    business_name: validatedFields.data.business_name,
    member_name: validatedFields.data.member_name,
    title: validatedFields.data.title,
    logo_url: validatedFields.data.logo_url,
    established_year: validatedFields.data.established_year,
    address_line: validatedFields.data.address_line,
    city: validatedFields.data.city,
    state: validatedFields.data.state,
    pincode: validatedFields.data.pincode,
    phone: validatedFields.data.phone,
    delivery_info: validatedFields.data.delivery_info,
    instagram_url: validatedFields.data.instagram_url,
    facebook_url: validatedFields.data.facebook_url,
    whatsapp_number: validatedFields.data.whatsapp_number,
    about_bio: validatedFields.data.about_bio,
    locality: validatedFields.data.locality,
    theme_color: validatedFields.data.theme_color,
    business_hours: businessHoursData as Json,
    status: validatedFields.data.status,
    business_slug: finalSlug,
    contact_email: validatedFields.data.contact_email,
    business_category: validatedFields.data.business_category,
    custom_branding: validatedFields.data.custom_branding as Json,
    custom_ads: validatedFields.data.custom_ads as Json,
  };

  // 7. Update the business profile in Supabase
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update(dataToUpdate)
    .eq("id", user.id)
    .select(
      `
      id, business_name, member_name, title, logo_url, address_line, city, state, pincode, locality,
      phone, instagram_url, facebook_url, whatsapp_number, about_bio, status, business_slug,
      theme_color, delivery_info, business_hours, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, total_likes, total_subscriptions, average_rating,
      business_category, custom_branding, custom_ads, established_year
    `
    )
    .single();

  if (updateError) {
    console.error("Supabase Update Error:", updateError);
    return {
      success: false,
      error: `Failed to update profile: ${updateError.message}`,
    };
  }

  if (!updatedProfile) {
    return {
      success: false,
      error: "Failed to update profile. Profile not found after update.",
    };
  }

  // 8. Update phone in Supabase auth.users table if phone was changed
  if (
    validatedFields.data.phone &&
    existingProfile &&
    validatedFields.data.phone !== existingProfile.phone
  ) {
    const { error: authUpdateError } = await supabase.auth.updateUser({
      phone: `+91${validatedFields.data.phone}`,
    });

    if (authUpdateError) {
      console.warn(
        "Failed to update auth phone field:",
        authUpdateError.message
      );
      // Don't fail the operation for this, just log the warning
      // The business_profiles table is updated successfully
    }
  }

  // 10. Return success response with the updated data
  return {
    success: true,
    data: {
      success: true,
      data: updatedProfile
    }
  };
}

/**
 * Updates basic business information section
 * @param basicInfo - Basic info data to update
 * @returns Success/error response
 */
export async function updateBasicInfo(basicInfo: {
  business_name: string;
  member_name: string;
  title: string;
  logo_url?: string;
  business_category?: string;
  established_year?: number;
  about_bio?: string;
}): Promise<{ success: boolean; error?: string; data?: any }> {
  // Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Update only basic info fields
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update({
      business_name: basicInfo.business_name,
      member_name: basicInfo.member_name,
      title: basicInfo.title,
      logo_url: basicInfo.logo_url,
      business_category: basicInfo.business_category,
      established_year: basicInfo.established_year,
      about_bio: basicInfo.about_bio,
    })
    .eq("id", user.id)
    .select()
    .single();

  if (updateError) {
    console.error("Basic info update error:", updateError);
    return { success: false, error: "Failed to update basic information." };
  }

  return { success: true, data: updatedProfile };
}

/**
 * Updates contact and location information section
 * @param contactInfo - Contact and location data to update
 * @returns Success/error response
 */
export async function updateContactLocation(contactInfo: {
  phone: string;
  contact_email: string;
  whatsapp_number?: string;
  address_line: string;
  city: string;
  state: string;
  pincode: string;
  locality: string;
  latitude?: number;
  longitude?: number;
}): Promise<{ success: boolean; error?: string; data?: any }> {
  // Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Get existing profile to compare phone numbers
  const { data: existingProfile, error: profileError } = await supabase
    .from("business_profiles")
    .select("phone")
    .eq("id", user.id)
    .single();

  if (profileError) {
    return { success: false, error: "Failed to fetch existing profile." };
  }

  // Update contact and location fields
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update({
      phone: contactInfo.phone,
      contact_email: contactInfo.contact_email,
      whatsapp_number: contactInfo.whatsapp_number,
      address_line: contactInfo.address_line,
      city: contactInfo.city,
      state: contactInfo.state,
      pincode: contactInfo.pincode,
      locality: contactInfo.locality,
      latitude: contactInfo.latitude,
      longitude: contactInfo.longitude,
    })
    .eq("id", user.id)
    .select()
    .single();

  if (updateError) {
    console.error("Contact location update error:", updateError);
    return { success: false, error: "Failed to update contact information." };
  }

  // Update phone in Supabase auth.users table if phone was changed
  if (contactInfo.phone && contactInfo.phone !== existingProfile.phone) {
    const { error: authUpdateError } = await supabase.auth.updateUser({
      phone: `+91${contactInfo.phone}`,
    });

    if (authUpdateError) {
      console.warn(
        "Failed to update auth phone field:",
        authUpdateError.message
      );
      // Don't fail the operation for this, just log the warning
    }
  }

  return { success: true, data: updatedProfile };
}

/**
 * Updates business details section (hours, delivery info)
 * @param businessDetails - Business details data to update
 * @returns Success/error response
 */
export async function updateBusinessDetails(businessDetails: {
  business_hours?: any;
  delivery_info?: string;
}): Promise<{ success: boolean; error?: string; data?: any }> {
  // Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Update business details fields
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update({
      business_hours: businessDetails.business_hours,
      delivery_info: businessDetails.delivery_info,
    })
    .eq("id", user.id)
    .select()
    .single();

  if (updateError) {
    console.error("Business details update error:", updateError);
    return { success: false, error: "Failed to update business details." };
  }

  return { success: true, data: updatedProfile };
}

/**
 * Updates social links section
 * @param socialLinks - Social links data to update
 * @returns Success/error response
 */
export async function updateSocialLinks(socialLinks: {
  instagram_url?: string;
  facebook_url?: string;
  whatsapp_number?: string;
}): Promise<{ success: boolean; error?: string; data?: any }> {
  // Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Update social links fields
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update({
      instagram_url: socialLinks.instagram_url,
      facebook_url: socialLinks.facebook_url,
      whatsapp_number: socialLinks.whatsapp_number,
    })
    .eq("id", user.id)
    .select()
    .single();

  if (updateError) {
    console.error("Social links update error:", updateError);
    return { success: false, error: "Failed to update social links." };
  }

  return { success: true, data: updatedProfile };
}

/**
 * Updates appearance section (theme color)
 * @param appearance - Appearance data to update
 * @returns Success/error response
 */
export async function updateAppearance(appearance: {
  theme_color: string;
}): Promise<{ success: boolean; error?: string; data?: any }> {
  // Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // Update appearance fields
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update({
      theme_color: appearance.theme_color,
    })
    .eq("id", user.id)
    .select()
    .single();

  if (updateError) {
    console.error("Appearance update error:", updateError);
    return { success: false, error: "Failed to update appearance." };
  }

  return { success: true, data: updatedProfile };
}

/**
 * Updates status and settings section
 * @param statusSettings - Status and settings data to update
 * @returns Success/error response
 */
export async function updateStatusSettings(statusSettings: {
  status: "online" | "offline";
  business_slug?: string;
}): Promise<{ success: boolean; error?: string; data?: any }> {
  // Get the authenticated user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  // If going online, validate required fields and generate slug
  let finalSlug = statusSettings.business_slug;

  if (statusSettings.status === "online") {
    // Get current profile to check required fields
    const { data: currentProfile, error: profileError } = await supabase
      .from("business_profiles")
      .select("business_name, phone, address_line, city, state, pincode")
      .eq("id", user.id)
      .single();

    if (profileError) {
      return { success: false, error: "Failed to fetch profile data." };
    }

    // Check required fields for going online
    const requiredFields = ["business_name", "phone", "address_line", "city", "state", "pincode"] as const;
    const missingFields = requiredFields.filter(field => !currentProfile[field as keyof typeof currentProfile]);

    if (missingFields.length > 0) {
      return {
        success: false,
        error: `Cannot go online. Missing required fields: ${missingFields.join(", ")}`
      };
    }

    // Generate unique slug if needed
    if (!finalSlug) {
      // Simple slug generation - in production, you'd want the full generateUniqueSlug logic
      finalSlug = currentProfile.business_name
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
    }
  }

  // Update status and settings fields
  const { data: updatedProfile, error: updateError } = await supabase
    .from("business_profiles")
    .update({
      status: statusSettings.status,
      business_slug: finalSlug,
    })
    .eq("id", user.id)
    .select()
    .single();

  if (updateError) {
    console.error("Status settings update error:", updateError);
    return { success: false, error: "Failed to update status settings." };
  }

  return { success: true, data: updatedProfile };
}

// ============================================================================
// SECTION-SPECIFIC DATA FETCHING FUNCTIONS
// ============================================================================

/**
 * Fetches basic info section data only
 * @returns Basic info data or error
 */
export async function getBasicInfoData(): Promise<{
  data?: {
    business_name: string;
    member_name: string;
    title: string;
    logo_url?: string;
    business_category?: string;
    established_year?: number;
    about_bio?: string;
  };
  error?: string;
  success: boolean;
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { data, error } = await supabase
    .from("business_profiles")
    .select("business_name, member_name, title, logo_url, business_category, established_year, about_bio")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Basic info fetch error:", error);
    return { success: false, error: "Failed to fetch basic information." };
  }

  return { success: true, data };
}

/**
 * Fetches contact and location section data only
 * @returns Contact and location data or error
 */
export async function getContactLocationData(): Promise<{
  data?: {
    phone: string;
    contact_email: string;
    whatsapp_number?: string;
    address_line: string;
    city: string;
    state: string;
    pincode: string;
    locality: string;
    latitude?: number;
    longitude?: number;
  };
  error?: string;
  success: boolean;
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { data, error } = await supabase
    .from("business_profiles")
    .select("phone, contact_email, whatsapp_number, address_line, city, state, pincode, locality, latitude, longitude")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Contact location fetch error:", error);
    return { success: false, error: "Failed to fetch contact and location information." };
  }

  return { success: true, data };
}

/**
 * Fetches business details section data only
 * @returns Business details data or error
 */
export async function getBusinessDetailsData(): Promise<{
  data?: {
    business_hours?: any;
    delivery_info?: string;
  };
  error?: string;
  success: boolean;
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { data, error } = await supabase
    .from("business_profiles")
    .select("business_hours, delivery_info")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Business details fetch error:", error);
    return { success: false, error: "Failed to fetch business details." };
  }

  return { success: true, data };
}

/**
 * Fetches social links section data only
 * @returns Social links data or error
 */
export async function getSocialLinksData(): Promise<{
  data?: {
    instagram_url?: string;
    facebook_url?: string;
    whatsapp_number?: string;
  };
  error?: string;
  success: boolean;
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { data, error } = await supabase
    .from("business_profiles")
    .select("instagram_url, facebook_url, whatsapp_number")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Social links fetch error:", error);
    return { success: false, error: "Failed to fetch social links." };
  }

  return { success: true, data };
}

/**
 * Fetches status and settings section data only
 * @returns Status and settings data or error
 */
export async function getStatusSettingsData(): Promise<{
  data?: {
    status: "online" | "offline";
    business_slug?: string;
  };
  error?: string;
  success: boolean;
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { data, error } = await supabase
    .from("business_profiles")
    .select("status, business_slug")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Status settings fetch error:", error);
    return { success: false, error: "Failed to fetch status settings." };
  }

  return { success: true, data };
}

/**
 * Fetches appearance section data only
 * @returns Appearance data or error
 */
export async function getAppearanceData(): Promise<{
  data?: {
    theme_color?: string;
  };
  error?: string;
  success: boolean;
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { data, error } = await supabase
    .from("business_profiles")
    .select("theme_color")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Appearance fetch error:", error);
    return { success: false, error: "Failed to fetch appearance settings." };
  }

  return { success: true, data };
}

/**
 * Fetches advanced features section data only
 * @returns Advanced features data or error
 */
export async function getAdvancedFeaturesData(): Promise<{ 
  success: boolean; 
  error?: string; 
  data?: { 
    custom_branding?: Json; 
    custom_ads?: Json; 
  } 
}> {
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const { data, error } = await supabase
    .from("business_profiles")
    .select("custom_branding, custom_ads")
    .eq("id", user.id)
    .single();

  if (error) {
    console.error("Advanced features fetch error:", error);
    return { success: false, error: "Failed to fetch advanced features." };
  }

  return { success: true, data };
}
