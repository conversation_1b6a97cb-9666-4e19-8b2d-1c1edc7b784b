/**
 * BusinessCard component for React Native Discovery Screen
 * Based on dukancard/app/(main)/discover/components/AnimatedBusinessCard.tsx
 */

import React from "react";
import { View, Text, TouchableOpacity, Image, StyleSheet } from "react-native";
import { MapPin, Heart, UserPlus, Star } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { BusinessCardData } from "@/src/types/business";
import {
  formatDistance,
  calculateDistanceWithFallback,
} from "@/src/utils/distanceCalculation";
import { formatIndianNumberShort } from "@/lib/utils";
import { useLocation } from "@/src/contexts/LocationContext";

interface BusinessCardProps {
  business: BusinessCardData;
  onPress: (business: BusinessCardData) => void;
  showDistance?: boolean;
}

export const BusinessCard: React.FC<BusinessCardProps> = ({
  business,
  onPress,
  showDistance = true,
}) => {
  const { colors } = useTheme();
  const { currentLocation } = useLocation();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  const handlePress = () => {
    onPress(business);
  };

  const getLocationText = () => {
    // Only show locality, pincode, and city as requested
    const locationParts = [
      business.locality,
      business.city,
      business.pincode ? `${business.pincode}` : null,
    ].filter(Boolean);

    return locationParts.length > 0
      ? locationParts.join(", ")
      : "Location not specified";
  };

  const getBusinessInitial = () => {
    return business.business_name?.charAt(0)?.toUpperCase() || "B";
  };

  const getDistanceText = () => {
    if (!showDistance || !business.latitude || !business.longitude) {
      return null;
    }

    // Calculate distance using current GPS location with fallback to profile location
    const distance = calculateDistanceWithFallback(
      currentLocation,
      null, // We don't have profile location in BusinessCard, could be added if needed
      null,
      business.latitude,
      business.longitude
    );

    if (distance !== null) {
      return formatDistance(distance);
    }

    return null;
  };

  const styles = createStyles(colors);

  return (
    <TouchableOpacity
      style={styles.card}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {/* Distance Badge - Top Right */}
      {getDistanceText() && (
        <View
          style={[
            styles.distanceBadge,
            { backgroundColor: isDark ? "#000000" : "#FFFFFF" },
          ]}
        >
          <Text
            style={[
              styles.distanceBadgeText,
              { color: isDark ? "#FFFFFF" : "#000000" },
            ]}
          >
            {getDistanceText()}
          </Text>
        </View>
      )}

      {/* Header with logo and basic info */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          {business.logo_url ? (
            <Image
              source={{ uri: business.logo_url }}
              style={styles.logo}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>{getBusinessInitial()}</Text>
            </View>
          )}
        </View>

        <View style={styles.businessInfo}>
          <Text style={styles.businessName} numberOfLines={2}>
            {business.business_name}
          </Text>

          {business.business_category && (
            <Text style={styles.businessCategory} numberOfLines={1}>
              {business.business_category}
            </Text>
          )}

          {/* Location - only locality, city, pincode */}
          <View style={styles.locationContainer}>
            <MapPin size={12} color={colors.textSecondary} />
            <Text style={styles.locationText} numberOfLines={1}>
              {getLocationText()}
            </Text>
          </View>
        </View>
      </View>

      {/* Description */}
      {business.about_bio && (
        <Text style={styles.description} numberOfLines={2}>
          {business.about_bio}
        </Text>
      )}

      {/* Stats Section - Likes, Followers, Rating */}
      <View style={styles.statsContainer}>
        {/* Likes */}
        <View style={styles.statItem}>
          <Heart size={14} color="#ef4444" style={styles.statIcon} />
          <Text style={styles.statText}>
            {formatIndianNumberShort(business.total_likes ?? 0)}
          </Text>
        </View>

        {/* Followers/Subscribers */}
        <View style={styles.statItem}>
          <UserPlus size={14} color="#3b82f6" style={styles.statIcon} />
          <Text style={styles.statText}>
            {formatIndianNumberShort(business.total_subscriptions ?? 0)}
          </Text>
        </View>

        {/* Rating */}
        <View style={styles.statItem}>
          <Star size={14} color="#eab308" style={styles.statIcon} />
          <Text style={styles.statText}>
            {(business.average_rating ?? 0).toFixed(1)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    card: {
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 12, // Reduced from 16
      marginHorizontal: 0, // Removed horizontal margin
      marginVertical: 6, // Reduced from 8
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    header: {
      flexDirection: "row",
      marginBottom: 8, // Reduced from 12
      alignItems: "flex-start",
    },
    logoContainer: {
      marginRight: 10, // Reduced from 12
    },
    logo: {
      width: 44, // Reduced from 50
      height: 44, // Reduced from 50
      borderRadius: 22, // Adjusted for new size
      borderWidth: 1.5, // Reduced from 2
      borderColor: colors.primary + "30",
    },
    logoPlaceholder: {
      width: 44, // Reduced from 50
      height: 44, // Reduced from 50
      borderRadius: 22, // Adjusted for new size
      backgroundColor: colors.primary + "20",
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1.5, // Reduced from 2
      borderColor: colors.primary + "30",
    },
    logoText: {
      fontSize: 16, // Reduced from 18
      fontWeight: "600",
      color: colors.primary,
    },
    businessInfo: {
      flex: 1,
    },
    businessName: {
      fontSize: 15, // Reduced from 16
      fontWeight: "600",
      color: colors.textPrimary,
      marginBottom: 3, // Reduced from 4
      lineHeight: 18, // Reduced from 20
    },
    businessCategory: {
      fontSize: 11, // Reduced from 12
      color: colors.textSecondary,
      marginBottom: 4,
      textTransform: "capitalize",
    },
    locationContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 2,
    },
    locationText: {
      fontSize: 11,
      color: colors.textSecondary,
      marginLeft: 4,
      flex: 1,
    },

    description: {
      fontSize: 13, // Reduced from 14
      color: colors.textSecondary,
      lineHeight: 16, // Reduced from 18
      marginTop: 4, // Reduced margin
    },
    statsContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      alignItems: "center",
      marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: colors.border + "40", // Faint border
    },
    statItem: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
    },
    statIcon: {
      marginRight: 4,
    },
    statText: {
      fontSize: 12,
      fontWeight: "600",
      color: colors.textPrimary, // Use textPrimary for better visibility in both light and dark mode
    },
    distanceBadge: {
      position: "absolute",
      top: 8,
      right: 8,
      paddingHorizontal: 10,
      paddingVertical: 6,
      zIndex: 1,
    },
    distanceBadgeText: {
      fontSize: 16,
      fontWeight: "800",
      textAlign: "center",
    },
  });
