import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import AboutTab from '@/src/components/business/AboutTab';
import { Linking, Alert } from 'react-native';
import { createPublicCardViewStyles } from '@/styles/PublicCardViewStyles';
import { BusinessProfileSelect, BusinessDiscoveryData } from '@/src/types/business';
import { BusinessProfile } from '@/src/types/business';

// Mock external modules
jest.mock('@/styles/PublicCardViewStyles', () => ({
  createPublicCardViewStyles: jest.fn(() => ({
    section: {},
    aboutSection: {},
    aboutText: {},
    categorySection: {},
    categoryTitle: {},
    aboutTableContainer: {},
    aboutTableRow: {},
    aboutTableLabel: {},
    aboutTableLabelText: {},
    aboutTableValue: {},
    statusBadge: {},
    statusText: {},
    callButton: {},
    callButtonText: {},
  })),
}));

// Mock Linking
const mockLinking = {
  openURL: jest.fn(() => Promise.resolve()),
  canOpenURL: jest.fn(() => Promise.resolve(true)),
};

jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Alert: {
      alert: jest.fn(),
    },
    Linking: mockLinking,
    // Mocking specific components to allow testID to be passed
    View: RN.View,
    Text: RN.Text,
    ScrollView: RN.ScrollView,
    TouchableOpacity: RN.TouchableOpacity,
  };
});

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  Package: 'PackageIcon',
  Users: 'UsersIcon',
  Calendar: 'CalendarIcon',
  Clock: 'ClockIcon',
  Phone: 'PhoneIcon',
  Mail: 'MailIcon',
  MapPin: 'MapPinIcon',
  Truck: 'TruckIcon',
  Navigation: 'NavigationIcon',
  MessageCircle: 'MessageCircleIcon',
  Globe: 'GlobeIcon',
}));

describe('AboutTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Ensure the mock returns a Promise
    mockLinking.openURL.mockReturnValue(Promise.resolve());
  });

  

  const mockBusinessData: BusinessDiscoveryData = {
    success: true,
    data: {
      about_bio: 'We are a business dedicated to providing the best services.',
      business_name: 'Test Business',
      member_name: 'John Doe',
      business_category: 'Retail',
      established_year: 2020,
      phone: '1234567890',
      whatsapp_number: '919876543210',
      facebook_url: 'https://facebook.com/testbusiness',
      instagram_url: 'https://instagram.com/testbusiness',
      address_line: '123 Main St',
      locality: 'Downtown',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400001',
      delivery_info: 'Free delivery within 5km',
      business_hours: {
        monday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
        tuesday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
        wednesday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
        thursday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
        friday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
        saturday: { isOpen: false },
        sunday: { isOpen: true, openTime: '10:00', closeTime: '14:00' },
      },
      average_rating: null,
      business_slug: null,
      city_slug: null,
      contact_email: null,
      created_at: '2023-01-01T00:00:00Z',
      custom_ads: null,
      custom_branding: null,
      gallery: null,
      google_maps_url: null,
      has_active_subscription: false,
      id: 'test-business-id',
      latitude: null,
      locality_slug: null,
      logo_url: null,
      longitude: null,
      state_slug: null,
      status: 'active',
      theme_color: null,
      title: null,
      today_visits: 0,
      total_likes: 0,
      total_subscriptions: 0,
      total_visits: 0,
      trial_end_date: null,
      updated_at: '2023-01-01T00:00:00Z',
      visits_30_days: 0,
      visits_7_days: 0,
      yesterday_visits: 0,
      user_plan: null,
    } as BusinessProfileSelect,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createPublicCardViewStyles as jest.Mock).mockReturnValue({
      section: {},
      aboutSection: {},
      aboutText: {},
      categorySection: {},
      categoryTitle: {},
      aboutTableContainer: {},
      aboutTableRow: {},
      aboutTableLabel: {},
      aboutTableLabelText: {},
      aboutTableValue: {},
      statusBadge: {},
      statusText: {},
      callButton: {},
      callButtonText: {},
    });
  });

  it('renders all sections when all data is provided', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);

    expect(screen.getByText('We are a business dedicated to providing the best services.')).toBeOnTheScreen();
    expect(screen.getByText('Business Information')).toBeOnTheScreen();
    expect(screen.getByText('Test Business')).toBeOnTheScreen();
    expect(screen.getByText('John Doe')).toBeOnTheScreen();
    expect(screen.getByText('Retail')).toBeOnTheScreen();
    expect(screen.getByText('2020')).toBeOnTheScreen();
    expect(screen.getByText('Open')).toBeOnTheScreen();
    expect(screen.getByText('Contact Details')).toBeOnTheScreen();
    expect(screen.getByTestId('call-now-button')).toBeOnTheScreen();
    expect(screen.getByText('123 Main St, Downtown, Mumbai, Maharashtra, 400001')).toBeOnTheScreen();
    expect(screen.getByText('Free delivery within 5km')).toBeOnTheScreen();
    expect(screen.getByText('Business Hours')).toBeOnTheScreen();
    expect(screen.getByText('Monday')).toBeOnTheScreen();
    expect(screen.getByText('9:00 AM - 5:00 PM')).toBeOnTheScreen();
    expect(screen.getByText('Saturday')).toBeOnTheScreen();
    expect(screen.getByText('Closed')).toBeOnTheScreen();
    expect(screen.getByText('Sunday')).toBeOnTheScreen();
    expect(screen.getByText('10:00 AM - 2:00 PM')).toBeOnTheScreen();
  });

  it('does not render About Bio section if about_bio is missing', () => {
    const dataWithoutBio: BusinessProfileSelect = { ...mockBusinessData.data!, about_bio: null };
    render(<AboutTab businessData={{...mockBusinessData, data: dataWithoutBio}} isDark={false} />);
    expect(screen.queryByText('We are a business dedicated to providing the best services.')).toBeNull();
  });

  it('does not render Phone Number section if phone is missing', () => {
    const dataWithoutPhone: BusinessProfileSelect = {
      about_bio: mockBusinessData.data!.about_bio,
      address_line: mockBusinessData.data!.address_line,
      average_rating: mockBusinessData.data!.average_rating,
      business_category: mockBusinessData.data!.business_category,
      business_hours: mockBusinessData.data!.business_hours,
      business_name: mockBusinessData.data!.business_name,
      business_slug: mockBusinessData.data!.business_slug,
      city: mockBusinessData.data!.city,
      city_slug: mockBusinessData.data!.city_slug,
      contact_email: mockBusinessData.data!.contact_email,
      created_at: mockBusinessData.data!.created_at,
      custom_ads: mockBusinessData.data!.custom_ads,
      custom_branding: mockBusinessData.data!.custom_branding,
      delivery_info: mockBusinessData.data!.delivery_info,
      established_year: mockBusinessData.data!.established_year,
      facebook_url: mockBusinessData.data!.facebook_url,
      gallery: mockBusinessData.data!.gallery,
      google_maps_url: mockBusinessData.data!.google_maps_url,
      has_active_subscription: mockBusinessData.data!.has_active_subscription,
      id: mockBusinessData.data!.id,
      instagram_url: mockBusinessData.data!.instagram_url,
      latitude: mockBusinessData.data!.latitude,
      locality: mockBusinessData.data!.locality,
      locality_slug: mockBusinessData.data!.locality_slug,
      logo_url: mockBusinessData.data!.logo_url,
      longitude: mockBusinessData.data!.longitude,
      member_name: mockBusinessData.data!.member_name,
      phone: null,
      pincode: mockBusinessData.data!.pincode,
      state: mockBusinessData.data!.state,
      state_slug: mockBusinessData.data!.state_slug,
      status: mockBusinessData.data!.status,
      theme_color: mockBusinessData.data!.theme_color,
      title: mockBusinessData.data!.title,
      today_visits: mockBusinessData.data!.today_visits,
      total_likes: mockBusinessData.data!.total_likes,
      total_subscriptions: mockBusinessData.data!.total_subscriptions,
      total_visits: mockBusinessData.data!.total_visits,
      trial_end_date: mockBusinessData.data!.trial_end_date,
      updated_at: mockBusinessData.data!.updated_at,
      visits_30_days: mockBusinessData.data!.visits_30_days,
      visits_7_days: mockBusinessData.data!.visits_7_days,
      whatsapp_number: mockBusinessData.data!.whatsapp_number,
      yesterday_visits: mockBusinessData.data!.yesterday_visits,
      user_plan: mockBusinessData.data!.user_plan,
    };
    render(<AboutTab businessData={{...mockBusinessData, data: dataWithoutPhone}} isDark={false} />);
    expect(screen.queryByTestId('call-now-button')).toBeNull();
  });

  it('does not render WhatsApp section if whatsapp_number is missing', () => {
    const dataWithoutWhatsApp: BusinessProfileSelect = {
      ...mockBusinessData.data!,
      whatsapp_number: null,
    };
    render(<AboutTab businessData={{...mockBusinessData, data: dataWithoutWhatsApp}} isDark={false} />);
    expect(screen.queryByTestId('whatsapp-button')).toBeNull();
  });

  it('does not render Facebook section if facebook_url is missing', () => {
    const dataWithoutFacebook: BusinessProfileSelect = {
      ...mockBusinessData.data!,
      facebook_url: null,
    };
    render(<AboutTab businessData={{...mockBusinessData, data: dataWithoutFacebook}} isDark={false} />);
    expect(screen.queryByTestId('facebook-button')).toBeNull();
  });

  it('does not render Instagram section if instagram_url is missing', () => {
    const dataWithoutInstagram: BusinessProfileSelect = { ...mockBusinessData.data!, instagram_url: null };
    render(<AboutTab businessData={{...mockBusinessData, data: dataWithoutInstagram}} isDark={false} />);
    expect(screen.queryByTestId('instagram-button')).toBeNull();
  });

  it('does not render Delivery Info section if delivery_info is missing', () => {
    const dataWithoutDelivery: BusinessProfileSelect = { ...mockBusinessData.data!, delivery_info: null };
    render(<AboutTab businessData={{...mockBusinessData, data: dataWithoutDelivery}} isDark={false} />);
    expect(screen.queryByText('Free delivery within 5km')).toBeNull();
  });

  it('handles phone call correctly', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('call-now-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith('tel:1234567890');
  });

  it('shows alert if phone call fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('call-now-button'));
    await new Promise(resolve => setTimeout(resolve, 0)); // Wait for promise to resolve
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to make phone call');
  });

  it('handles WhatsApp chat correctly', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('whatsapp-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith(
      'https://wa.me/919876543210?text=Hi%20Test%20Business%2C%20I%20found%20your%20business%20on%20Dukancard%20and%20would%20like%20to%20know%20more%20about%20your%20services.'
    );
  });

  it('shows alert if WhatsApp chat fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('whatsapp-button'));
    await new Promise(resolve => setTimeout(resolve, 0)); // Wait for promise to resolve
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to open WhatsApp');
  });

  it('handles Facebook link correctly', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('facebook-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith('https://facebook.com/testbusiness');
  });

  it('shows alert if Facebook link fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('facebook-button'));
    await new Promise(resolve => setTimeout(resolve, 0)); // Wait for promise to resolve
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to open Facebook');
  });

  it('handles Instagram link correctly', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('instagram-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith('https://instagram.com/testbusiness');
  });

  it('shows alert if Instagram link fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    fireEvent.press(screen.getByTestId('instagram-button'));
    await new Promise(resolve => setTimeout(resolve, 0)); // Wait for promise to resolve
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to open Instagram');
  });

  it('formats business hours correctly, including closed days', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    expect(screen.getByText(/Monday/)).toBeOnTheScreen();
    expect(screen.getByText(/9:00 AM - 5:00 PM/)).toBeOnTheScreen();
    expect(screen.getByText(/Saturday/)).toBeOnTheScreen();
    expect(screen.getByText(/Closed/)).toBeOnTheScreen();
    expect(screen.getByText(/Sunday/)).toBeOnTheScreen();
    expect(screen.getByText(/10:00 AM - 2:00 PM/)).toBeOnTheScreen();
  });

  it('does not render Business Hours section if business_hours is missing', () => {
    const dataWithoutHours = { ...mockBusinessData.data, business_name: 'Test Business', business_hours: null, about_bio: null, address_line: null, average_rating: null, business_category: null, business_slug: null, city: null, city_slug: null, contact_email: null, created_at: new Date().toISOString(), custom_ads: null, custom_branding: null, delivery_info: null, established_year: null, facebook_url: null, gallery: null, google_maps_url: null, has_active_subscription: false, id: 'test-id', instagram_url: null, latitude: null, locality: null, locality_slug: null, logo_url: null, longitude: null, member_name: null, phone: null, pincode: null, state: null, state_slug: null, status: 'active', theme_color: null, title: null, today_visits: 0, total_likes: 0, total_subscriptions: 0, total_visits: 0, trial_end_date: null, updated_at: new Date().toISOString(), visits_30_days: 0, visits_7_days: 0, whatsapp_number: null, yesterday_visits: 0 };
    render(<AboutTab businessData={{...mockBusinessData, data: dataWithoutHours}} isDark={false} />);
    expect(screen.queryByText('Business Hours')).toBeNull();
  });

  it('renders correctly in dark mode', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={true} />);
    // Expect styles to be called with isDark = true
    expect(createPublicCardViewStyles).toHaveBeenCalledWith(true);
  });

  it('renders correctly in light mode', () => {
    render(<AboutTab businessData={mockBusinessData} isDark={false} />);
    // Expect styles to be called with isDark = false
    expect(createPublicCardViewStyles).toHaveBeenCalledWith(false);
  });

  it('handles empty business data gracefully', () => {
    const emptyBusinessData: BusinessDiscoveryData = {
      success: true,
      data: {
        about_bio: null,
        address_line: null,
        average_rating: null,
        business_category: null,
        business_hours: null,
        business_name: '',
        business_slug: null,
        city: null,
        city_slug: null,
        contact_email: null,
        created_at: '2023-01-01T00:00:00Z',
        custom_ads: null,
        custom_branding: null,
        delivery_info: null,
        established_year: null,
        facebook_url: null,
        gallery: null,
        google_maps_url: null,
        has_active_subscription: false,
        id: 'empty-business-id',
        instagram_url: null,
        latitude: null,
        locality: null,
        locality_slug: null,
        logo_url: null,
        longitude: null,
        member_name: null,
        phone: null,
        pincode: null,
        state: null,
        state_slug: null,
        status: 'inactive',
        theme_color: null,
        title: null,
        today_visits: 0,
        total_likes: 0,
        total_subscriptions: 0,
        total_visits: 0,
        trial_end_date: null,
        updated_at: '2023-01-01T00:00:00Z',
        visits_30_days: 0,
        visits_7_days: 0,
        whatsapp_number: null,
        yesterday_visits: 0,
        user_plan: null,
      },
      error: undefined,
    };
    render(<AboutTab businessData={emptyBusinessData} isDark={false} />);
    expect(screen.getByText(/Business Information/)).toBeOnTheScreen(); // Title should still be there
    expect(screen.queryByText('Test Business')).toBeNull();
    expect(screen.getByText(/Contact Details/)).toBeOnTheScreen(); // Title should still be there
    expect(screen.queryByTestId('call-now-button')).toBeNull();
    expect(screen.queryByText(/Business Hours/)).toBeNull();
    expect(screen.queryByText(/Social Media/)).toBeNull();
  });
});