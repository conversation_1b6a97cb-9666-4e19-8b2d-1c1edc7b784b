"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessCardData, requiredFieldsForOnline } from "../schema";
import { mapBusinessCardData } from "../data/businessCardMapper";
import { checkForceOfflineStatus } from "../data/subscriptionChecker";

/**
 * Fetches business card data for the authenticated user
 * @returns Business card data or error
 */
export async function getBusinessCardData(): Promise<{
  data?: BusinessCardData;
  error?: string;
}> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { error: "User not authenticated." };
  }

  // Select columns including metrics data
  const { data, error } = await supabase
    .from("business_profiles")
    .select(
      `
      id, business_name, contact_email, has_active_subscription,
      trial_end_date, created_at, updated_at, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug,
      total_likes, total_subscriptions, average_rating, theme_color, delivery_info, business_hours,
      business_category, custom_branding, custom_ads, established_year
    `
    )
    .eq("id", user.id)
    .single(); // Use single as profile must exist for editing

  if (error) {
    if (error.code === "PGRST116") {
      return { data: undefined };
    }
    console.error("Supabase Fetch Error:", error);
    return { error: `Failed to fetch profile: ${error.message}` };
  }

  // Server-side check: Force offline if online but subscription is halted or missing required fields
  if (data.status === "online") {
    let shouldForceOffline = false;
    let reason = "";

    // Check subscription status
    const subscriptionCheck = await checkForceOfflineStatus(user.id, supabase);
    if (subscriptionCheck.shouldForceOffline) {
      shouldForceOffline = true;
      reason = subscriptionCheck.reason || "subscription issue";
    }

    // Check if all required fields are present
    const missingRequiredFields = requiredFieldsForOnline.filter(
      (field) => {
        const value = (data as Record<string, unknown>)[field];
        return !value || String(value).trim() === "";
      }
    );

    if (missingRequiredFields.length > 0) {
      shouldForceOffline = true;
      reason = `missing required fields: ${missingRequiredFields.join(", ")}`;
    }

    // Force offline if needed
    if (shouldForceOffline) {
      console.log(
        `User ${user.id} card forced offline due to ${reason}.`
      );
      const { error: updateError } = await supabase
        .from("business_profiles")
        .update({ status: "offline" })
        .eq("id", user.id);

      if (updateError) {
        console.error("Error forcing card offline:", updateError.message);
      } else {
        data.status = "offline";
      }
    }
  }

  // Map data using the shared mapper
  const mappedData = mapBusinessCardData(data as any);
  return { data: mappedData };
}
