import { useState, useCallback, useRef } from 'react';
import { useNetworkStatus } from '@/src/utils/networkStatus';
import { handleNetworkError, handleSupabaseError, logError, AppError } from '@/src/utils/errorHandling';
import { useToast } from '@/src/components/ui/Toast';
import { Alert } from 'react-native';

export interface AuthErrorState {
  isLoading: boolean;
  error: AppError | null;
  retryCount: number;
  canRetry: boolean;
}

export interface AuthErrorHandlerOptions {
  maxRetries?: number;
  showToastOnError?: boolean;
  showAlertOnError?: boolean;
  logErrors?: boolean;
  context?: string;
}

export interface RetryableOperation<T = any> {
  operation: () => Promise<T>;
  onSuccess?: (result: T) => void;
  onError?: (error: AppError) => void;
  onRetry?: (retryCount: number) => void;
  context?: string;
}

/**
 * Enhanced error handler hook specifically designed for authentication and onboarding flows
 * Provides network status checking, error categorization, retry mechanisms, and user feedback
 */
export function useAuthErrorHandler(options: AuthErrorHandlerOptions = {}) {
  const {
    maxRetries = 3,
    showToastOnError = true,
    showAlertOnError = false,
    logErrors = true,
    context = 'AuthFlow'
  } = options;

  const networkStatus = useNetworkStatus();
  console.log("[useAuthErrorHandler] networkStatus:", networkStatus);
  const toast = useToast();
  const retryTimeoutRef = useRef<number | null>(null);

  const [errorState, setErrorState] = useState<AuthErrorState>({
    isLoading: false,
    error: null,
    retryCount: 0,
    canRetry: true,
  });

  /**
   * Check if the device is online before performing operations
   */
  const checkConnectivity = useCallback((): boolean => {
    console.log('[useAuthErrorHandler] Checking connectivity. Online:', networkStatus.isConnected);
    if (!networkStatus.isConnected) {
      const networkError: AppError = {
        type: 'network',
        title: 'No Internet Connection',
        message: 'Please check your internet connection and try again.',
        code: 'NETWORK_OFFLINE',
        originalError: { networkStatus }
      };
      
      setErrorState(prev => ({ ...prev, error: networkError }));
      
      if (showToastOnError) {
        toast.error('No Internet Connection', 'Please check your connection and try again.');
      }
      
      if (showAlertOnError) {
        Alert.alert(
          'No Internet Connection',
          'Please check your internet connection and try again.',
          [{ text: 'OK' }]
        );
      }
      
      return false;
    }
    
    return true;
  }, [networkStatus, showToastOnError, showAlertOnError, toast]);

  /**
   * Process and categorize errors for better user experience
   */
  const processError = useCallback((error: any, operationContext?: string): AppError => {
    let processedError: AppError;

    // Handle network errors
    if (!networkStatus.isConnected || error.message?.includes('Network Error')) {
      processedError = handleNetworkError(error);
    }
    // Handle Supabase/database errors
    else if (error.code || error.message?.includes('supabase')) {
      processedError = handleSupabaseError(error);
    }
    // Handle timeout errors
    else if (error.message?.includes('timeout') || error.code === 'TIMEOUT') {
      processedError = {
        type: 'network',
        title: 'Request Timeout',
        message: 'The request took too long. Please check your connection and try again.',
        code: 'TIMEOUT',
        originalError: { error, operationContext }
      };
    }
    // Handle authentication specific errors
    else if (error.message?.includes('auth') || error.message?.includes('login')) {
      processedError = {
        type: 'unauthorized',
        title: 'Authentication Failed',
        message: error.message || 'Unable to authenticate. Please try again.',
        code: 'AUTH_FAILED',
        originalError: { error, operationContext }
      };
    }
    // Generic error fallback
    else {
      processedError = {
        type: 'generic',
        title: 'Something went wrong',
        message: error.message || 'An unexpected error occurred. Please try again.',
        code: 'GENERIC_ERROR',
        originalError: { error, operationContext }
      };
    }

    // Log error if enabled
    if (logErrors) {
      logError(processedError, `${context}${operationContext ? ` - ${operationContext}` : ''}`);
    }

    return processedError;
  }, [networkStatus.isConnected, logErrors, context]);

  /**
   * Execute an operation with comprehensive error handling and retry logic
   */
  const executeWithErrorHandling = useCallback(async <T>(
    { operation, onSuccess, onError, onRetry, context: opContext }: RetryableOperation<T>
  ): Promise<T | null> => {
    // Check connectivity first
    if (!checkConnectivity()) {
      return null;
    }

    setErrorState(prev => ({ 
      ...prev, 
      isLoading: true, 
      error: null 
    }));

    try {
      const result = await operation();
      
      // Reset error state on success
      setErrorState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: null, 
        retryCount: 0,
        canRetry: true 
      }));

      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (error) {
      const processedError = processError(error, opContext);
      
      setErrorState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: processedError,
        canRetry: prev.retryCount < maxRetries
      }));

      // Show user feedback
      if (showToastOnError) {
        toast.error(processedError.title, processedError.message);
      }

      if (showAlertOnError) {
        Alert.alert(processedError.title, processedError.message, [{ text: 'OK' }]);
      }

      if (onError) {
        onError(processedError);
      }

      return null;
    }
  }, [checkConnectivity, processError, maxRetries, showToastOnError, showAlertOnError, toast]);

  /**
   * Retry the last failed operation
   */
  const retryOperation = useCallback(async <T>(
    retryableOp: RetryableOperation<T>
  ): Promise<T | null> => {
    if (!errorState.canRetry || errorState.retryCount >= maxRetries) {
      return null;
    }

    const newRetryCount = errorState.retryCount + 1;
    
    setErrorState(prev => ({ 
      ...prev, 
      retryCount: newRetryCount,
      canRetry: newRetryCount < maxRetries
    }));

    if (retryableOp.onRetry) {
      retryableOp.onRetry(newRetryCount);
    }

    // Add a small delay before retry
    return new Promise((resolve) => {
      retryTimeoutRef.current = setTimeout(async () => {
        const result = await executeWithErrorHandling(retryableOp);
        resolve(result);
      }, 1000 * newRetryCount); // Exponential backoff
    });
  }, [errorState.canRetry, errorState.retryCount, maxRetries, executeWithErrorHandling]);

  /**
   * Clear current error state
   */
  const clearError = useCallback(() => {
    setErrorState(prev => ({ 
      ...prev, 
      error: null, 
      retryCount: 0, 
      canRetry: true 
    }));
  }, []);

  /**
   * Reset the entire error handler state
   */
  const reset = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    
    setErrorState({
      isLoading: false,
      error: null,
      retryCount: 0,
      canRetry: true,
    });
  }, []);

  return {
    // State
    ...errorState,
    networkStatus,
    
    // Methods
    executeWithErrorHandling,
    retryOperation,
    clearError,
    reset,
    checkConnectivity,
    
    // Utilities
    isOnline: networkStatus.isConnected,
    hasError: !!errorState.error,
    errorMessage: errorState.error?.message,
    errorTitle: errorState.error?.title,
    canRetryOperation: errorState.canRetry && errorState.retryCount < maxRetries,
  };
}
