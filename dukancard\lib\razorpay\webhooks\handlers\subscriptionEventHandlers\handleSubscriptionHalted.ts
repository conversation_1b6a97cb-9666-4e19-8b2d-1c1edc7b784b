import { RazorpayWebhookData, RazorpaySubscription } from "../../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/server";
import {
  isTerminalStatus,
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";
import { updateSubscriptionWithBusinessProfile } from "../subscription-db-updater";

/**
 * Handle subscription.halted event
 *
 * This event is triggered when a subscription is halted due to payment failure.
 * The subscription is temporarily downgraded to free plan while preserving original plan details.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionHalted(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription halted: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.halted',
      eventId: razorpayEventId || `halted_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    const adminClient = await createClient();

    // Check current subscription status and plan details
    const { data: currentSubscription, error: fetchError } = await adminClient
      .from("payment_subscriptions")
      .select("subscription_status, plan_id, plan_cycle, business_profile_id")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (fetchError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${subscriptionId}:`, fetchError);
      return { success: false, message: `Error fetching subscription: ${fetchError.message}` };
    }

    if (!currentSubscription) {
      console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${subscriptionId}, skipping halted processing`);
      return { success: true, message: "No subscription found to halt" };
    }

    // Check if subscription is already in a terminal state
    const isTerminalState = currentSubscription.plan_id === "free" ||
                           isTerminalStatus(currentSubscription.subscription_status);

    if (isTerminalState) {
      console.log(`[RAZORPAY_WEBHOOK] Subscription ${subscriptionId} is in terminal state (plan_id: ${currentSubscription.plan_id}, status: ${currentSubscription.subscription_status}), skipping halted update`);
      return { success: true, message: "Subscription is in terminal state, skipping halted update" };
    }

    // Halt subscription: temporarily downgrade to free but preserve original plan
    console.log(`[RAZORPAY_WEBHOOK] Halting subscription ${subscriptionId}, preserving original plan ${currentSubscription.plan_id}/${currentSubscription.plan_cycle}`);

    const now = new Date().toISOString();
    const updateResult = await updateSubscriptionWithBusinessProfile({
      subscription_id: subscriptionId,
      business_profile_id: currentSubscription.business_profile_id,
      subscription_status: 'halted',
      has_active_subscription: false, // Halted users lose access
      additional_data: {
        plan_id: 'free', // Temporarily downgrade to free
        plan_cycle: 'monthly', // Free plan is always monthly
        original_plan_id: currentSubscription.plan_id, // Preserve original plan
        original_plan_cycle: currentSubscription.plan_cycle, // Preserve original cycle
        subscription_paused_at: now,
        updated_at: now
      }
    });

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, "Subscription halted and temporarily downgraded to free plan");
      console.log(`[RAZORPAY_WEBHOOK] Successfully halted subscription ${subscriptionId} with original plan preserved`);
      return { success: true, message: "Subscription halted and temporarily downgraded to free plan" };
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
      console.error(`[RAZORPAY_WEBHOOK] Failed to halt subscription ${subscriptionId}:`, updateResult.message);
      return updateResult;
    }
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription halted:", error);
    return {
      success: false,
      message: `Error handling subscription halted: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}