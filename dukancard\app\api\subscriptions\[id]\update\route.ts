import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";

import { updateSubscription } from "@/lib/razorpay/services/subscription";
import { getRazorpayPlanId } from "@/lib/config/plans";
import { PlanType, PlanCycle } from "@/lib/config/plans";
import { SubscriptionStateManager } from "@/lib/razorpay/webhooks/handlers/subscription-state-manager";

/**
 * PATCH /api/subscriptions/:id/update
 *
 * Updates a subscription in Razorpay and the database record
 *
 * Request body:
 * ```json
 * {
 *   "planId": "basic",
 *   "planCycle": "monthly",
 *   "quantity": 1,
 *   "scheduleChangeAt": "now",
 *   "customerNotify": true,
 *   "notes": {
 *     "note_key_1": "Note value 1",
 *     "note_key_2": "Note value 2"
 *   }
 * }
 * ```
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000002",
 *     "customer_id": "cust_00000000000001",
 *     "status": "active",
 *     "current_start": 1580453311,
 *     "current_end": 1581013800,
 *     "ended_at": null,
 *     "quantity": 5,
 *     "notes": {
 *       "note_key_1": "Note value 1",
 *       "note_key_2": "Note value 2"
 *     },
 *     "charge_at": 1580453311,
 *     "start_at": 1580453311,
 *     "end_at": 1606588200,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 0,
 *     "customer_notify": true,
 *     "created_at": 1580283807,
 *     "expire_by": 1580626111,
 *     "short_url": "https://rzp.io/i/yeDkUKy",
 *     "has_scheduled_changes": false,
 *     "change_scheduled_at": null,
 *     "source": "api",
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "remaining_count": 6,
 *     "db_subscription": {
 *       "id": "123e4567-e89b-12d3-a456-426614174000",
 *       "plan_id": "basic",
 *       "plan_cycle": "monthly",
 *       "updated_at": "2023-01-01T00:00:00Z"
 *     }
 *   }
 * }
 * ```
 */
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string  }> }) {
  try {
    // Get the subscription ID from the URL params
    const { id: subscriptionId } = await params;

    // Parse request body
    const body = await request.json();
    const {
      planId,
      planCycle,
      quantity,
      scheduleChangeAt = "now",
      customerNotify = true,
      remainingCount,
      startAt,
      offerId,
      notes = {}
    } = body;

    // Verify authentication using Supabase
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if the user has access to this subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (subscriptionError) {
      // If no subscription is found with this ID, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "Subscription not found" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user is authorized to update this subscription
    const isOwner = subscription.business_profile_id === user.id;

    if (!isOwner) {
      return NextResponse.json(
        { success: false, error: "Unauthorized to update this subscription" },
        { status: 403 }
      );
    }

    // Check if the subscription is in a state that can be updated using the new SubscriptionStateManager
    if (!SubscriptionStateManager.isActivePaidSubscription(subscription.subscription_status, subscription.plan_id)) {
      return NextResponse.json(
        { success: false, error: "Subscription cannot be updated in its current state" },
        { status: 400 }
      );
    }

    // Prepare update parameters
    const updateParams: Record<string, unknown> = {
      schedule_change_at: scheduleChangeAt,
      customer_notify: customerNotify
    };

    // Add optional parameters if provided
    if (planId && planCycle) {
      const razorpayPlanId = getRazorpayPlanId(
        planId as PlanType,
        planCycle as PlanCycle
      );

      if (!razorpayPlanId) {
        return NextResponse.json(
          { success: false, error: "Invalid plan or plan cycle" },
          { status: 400 }
        );
      }

      updateParams.plan_id = razorpayPlanId;

      // Check if we're changing billing cycles and set appropriate remaining_count
      const currentPlanCycle = subscription.plan_cycle;
      if (currentPlanCycle && currentPlanCycle !== planCycle) {
        console.log(`[SUBSCRIPTION_UPDATE] Billing cycle change detected: ${currentPlanCycle} -> ${planCycle}`);

        // Set remaining count based on target cycle to give 10 years of service
        if (planCycle === "monthly") {
          updateParams.remaining_count = 120; // 10 years in months
        } else if (planCycle === "yearly") {
          updateParams.remaining_count = 10; // 10 years
        }

        console.log(`[SUBSCRIPTION_UPDATE] Set remaining_count to ${updateParams.remaining_count} for ${planCycle} cycle`);
      }
    }

    if (quantity !== undefined) {
      updateParams.quantity = quantity;
    }

    // Only set remainingCount from request if no billing cycle change is detected
    // Billing cycle change logic takes precedence
    if (remainingCount !== undefined && !updateParams.remaining_count) {
      updateParams.remaining_count = remainingCount;
    }

    if (startAt !== undefined) {
      updateParams.start_at = startAt;
    }

    if (offerId !== undefined) {
      updateParams.offer_id = offerId;
    }

    if (Object.keys(notes).length > 0) {
      updateParams.notes = notes;
    }

    // Update the subscription in Razorpay
    const result = await updateSubscription(subscriptionId, updateParams);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Only update database if Razorpay update was successful
    console.log("[SUBSCRIPTION_UPDATE] Razorpay update successful, updating database...");

    

    // Update the subscription record in the database
    const now = new Date().toISOString();
    const dbUpdateParams: Record<string, unknown> = {
      updated_at: now
    };

    // Update plan details if provided
    if (planId) {
      dbUpdateParams.plan_id = planId;
    }

    if (planCycle) {
      dbUpdateParams.plan_cycle = planCycle;
    }

    // Determine if subscription should have active status after update
    // Import edge-safe subscription logic to determine has_active_subscription
    const { EdgeSubscriptionStateManager } = await import('@/lib/subscription/edge-validation');
    const hasActiveSubscription = EdgeSubscriptionStateManager.shouldHaveActiveSubscription(
      subscription.subscription_status,
      planId || subscription.plan_id
    );

    // ENHANCED: Use atomic RPC function for transaction safety
    const { data: atomicResult, error: atomicError } = await supabase.rpc('update_subscription_atomic', {
      p_additional_data: dbUpdateParams as any,
      p_business_profile_id: subscription.business_profile_id,
      p_has_active_subscription: hasActiveSubscription, // Update based on new plan
      p_new_status: subscription.subscription_status, // Keep current status
      p_subscription_id: subscriptionId,
      p_webhook_timestamp: undefined
    });

    if (atomicError || !(atomicResult as any)?.success) {
      console.error("[RAZORPAY_ERROR] Error updating subscription atomically:", atomicError || (atomicResult as any)?.error);
      // Continue anyway, as the Razorpay subscription was updated successfully
    } else {
      console.log("[SUBSCRIPTION_UPDATE] Database update successful");
    }

    // Return the updated subscription with additional database information
    return NextResponse.json(
      {
        success: true,
        data: {
          ...result.data,
          db_subscription: {
            id: subscription.id,
            plan_id: planId || subscription.plan_id,
            plan_cycle: planCycle || subscription.plan_cycle,
            updated_at: now
          }
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error updating subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
