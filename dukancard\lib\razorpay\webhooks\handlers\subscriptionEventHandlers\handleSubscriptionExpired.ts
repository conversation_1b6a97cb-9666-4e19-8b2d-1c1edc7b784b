import { RazorpayWebhookData, RazorpaySubscription } from "../../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/server";
import {
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";
import { updateSubscriptionWithBusinessProfile } from "../subscription-db-updater";

/**
 * Handle subscription.expired event
 *
 * This event is triggered when a subscription expires.
 * For trial users, this transitions them to free plan.
 * For paid users, this also transitions them to free plan.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionExpired(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription expired: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.expired',
      eventId: razorpayEventId || `expired_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    const adminClient = await createClient();

    // Check current subscription status to determine proper handling
    const { data: currentSubscription, error: fetchError } = await adminClient
      .from('payment_subscriptions')
      .select('subscription_status, plan_id, business_profile_id')
      .eq('razorpay_subscription_id', subscriptionId)
      .maybeSingle();

    if (fetchError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${subscriptionId}:`, fetchError);
      return { success: false, message: `Error fetching subscription: ${fetchError.message}` };
    }

    if (!currentSubscription) {
      console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${subscriptionId}, skipping expiry processing`);
      return { success: true, message: "No subscription found to expire" };
    }

    // For trial users or any expired subscription, transition to free plan
    console.log(`[RAZORPAY_WEBHOOK] Transitioning expired subscription ${subscriptionId} to free plan`);

    const now = new Date().toISOString();
    const updateResult = await updateSubscriptionWithBusinessProfile({
      subscription_id: subscriptionId,
      business_profile_id: currentSubscription.business_profile_id,
      subscription_status: 'active', // Set to active but with free plan
      has_active_subscription: false, // Free plan users don't have "active subscription"
      additional_data: {
        plan_id: 'free',
        plan_cycle: 'monthly',
        subscription_start_date: now,
        subscription_expiry_time: null, // Free plan doesn't expire
        subscription_charge_time: null, // Free plan doesn't charge
        cancelled_at: now,
        // Clear Razorpay-related fields
        razorpay_subscription_id: null,
        razorpay_customer_id: null,
        razorpay_plan_id: null,
        last_payment_id: null,
        last_payment_date: null,
        last_payment_method: null,
        updated_at: now
      }
    });

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, "Subscription expired and transitioned to free plan");
      console.log(`[RAZORPAY_WEBHOOK] Successfully transitioned subscription ${subscriptionId} to free plan`);
      return { success: true, message: "Subscription expired and transitioned to free plan" };
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
      console.error(`[RAZORPAY_WEBHOOK] Failed to transition subscription ${subscriptionId} to free plan:`, updateResult.message);
      return updateResult;
    }
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription expired:", error);
    return {
      success: false,
      message: `Error handling subscription expired: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}