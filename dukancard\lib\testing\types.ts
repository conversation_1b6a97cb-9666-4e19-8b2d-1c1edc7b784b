export type SubscriptionStatus = 
  | 'active'
  | 'trialing'
  | 'past_due'
  | 'canceled'
  | 'unpaid'
  | 'incomplete'
  | 'incomplete_expired'
  | 'paused'
  | 'unknown';

export type PlanType = 'free' | 'basic' | 'growth' | 'enterprise';
export type PlanCycle = 'monthly' | 'yearly';

export interface DatabaseState {
  subscriptionStatus: string;
  planId: string;
  hasActiveSubscription: boolean;
  lastWebhookTimestamp?: string;
}

export interface ProcessedWebhookEvent {
  event_id: string;
  event_type: string | null;
  entity_type: string | null;
  entity_id: string | null;
  status: string | null;
  payload: Record<string, unknown>;
  created_at: string | null;
  notes?: string | null;
}
