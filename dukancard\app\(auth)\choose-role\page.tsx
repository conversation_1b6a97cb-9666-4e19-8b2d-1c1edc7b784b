import React from "react";
import { Metada<PERSON> } from "next";
import ChooseRoleClient from "./ChooseRoleClient";
import { getAuthenticatedUser, checkIfCustomerProfileExists, checkIfBusinessProfileExists } from "@/lib/supabase/services/userService";
import { redirect as nextRedirect } from "next/navigation";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Choose Your Role", // Uses template: "Choose Your Role - Dukancard"
    description: "Select how you will use Dukancard.",
    robots: "noindex, nofollow", // Keep preventing indexing and following
  };
}

// This page should only be accessible to logged-in users without a profile
export default async function ChooseRolePage({
  searchParams,
}: {
  searchParams: Promise<{ redirect?: string; message?: string }>;
}) {
  const { redirect, message } = await searchParams;
  const redirectSlug = redirect || null;
  const messageParam = message || null;

  const { user, error: userError } = await getAuthenticatedUser();

  if (!user) {
    // Should be handled by middleware, but good safeguard
    return nextRedirect("/login");
  }

  // Check if profile already exists in either table (middleware should prevent this, but double-check)
  const [customerProfileRes, businessProfileRes] = await Promise.all([
    checkIfCustomerProfileExists(user.id),
    checkIfBusinessProfileExists(user.id),
  ]);

  if (customerProfileRes.error || businessProfileRes.error) {
    // Handle error appropriately - redirect to login for safety
    return nextRedirect("/login?message=Error checking profile status");
  }

  if (customerProfileRes.exists || businessProfileRes.exists) {
    // User already has a profile, redirect them away
    const userType = customerProfileRes.exists ? "customer" : "business";
    const redirectPath =
      userType === "business" ? "/dashboard/business" : "/dashboard/customer";
    return nextRedirect(redirectPath);
  }

  // If no profile exists and user is logged in, render the choice component
  // This div acts as a minimal layout for this specific route
  return (
      <ChooseRoleClient userId={user.id} redirectSlug={redirectSlug} message={messageParam} />
  );
}
